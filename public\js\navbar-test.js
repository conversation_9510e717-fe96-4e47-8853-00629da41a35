/**
 * Script de test pour la navbar LocaSpace
 * Permet de tester le comportement responsive et les fonctionnalités
 */

class NavbarTester {
    constructor() {
        this.navbar = document.querySelector('.navbar');
        this.toggler = document.querySelector('.navbar-toggler');
        this.collapse = document.querySelector('.navbar-collapse');
        this.dropdowns = document.querySelectorAll('.dropdown-toggle');
        
        this.init();
    }
    
    init() {
        console.log('🔧 NavbarTester initialisé');
        this.logNavbarInfo();
        this.setupEventListeners();
    }
    
    logNavbarInfo() {
        console.log('📊 Informations Navbar:');
        console.log('- Largeur écran:', window.innerWidth + 'px');
        console.log('- Type écran:', this.getScreenType());
        console.log('- Navbar présente:', !!this.navbar);
        console.log('- Toggler présent:', !!this.toggler);
        console.log('- Collapse présent:', !!this.collapse);
        console.log('- Nombre de dropdowns:', this.dropdowns.length);
    }
    
    getScreenType() {
        const width = window.innerWidth;
        if (width >= 1200) return 'XL (≥1200px)';
        if (width >= 992) return 'LG (≥992px)';
        if (width >= 768) return 'MD (≥768px)';
        if (width >= 576) return 'SM (≥576px)';
        return 'XS (<576px)';
    }
    
    setupEventListeners() {
        // Test au redimensionnement
        window.addEventListener('resize', () => {
            console.log('📱 Redimensionnement:', this.getScreenType());
        });
        
        // Test des dropdowns
        this.dropdowns.forEach((dropdown, index) => {
            dropdown.addEventListener('click', () => {
                console.log(`🔽 Dropdown ${index + 1} cliqué`);
            });
        });
        
        // Test du toggler
        if (this.toggler) {
            this.toggler.addEventListener('click', () => {
                console.log('🍔 Bouton hamburger cliqué');
                setTimeout(() => {
                    const isExpanded = this.collapse.classList.contains('show');
                    console.log('📋 Menu', isExpanded ? 'ouvert' : 'fermé');
                }, 100);
            });
        }
    }
    
    // Méthodes de test publiques
    testResponsive() {
        const results = {
            screenWidth: window.innerWidth,
            screenType: this.getScreenType(),
            navbarPresent: !!this.navbar,
            togglerPresent: !!this.toggler,
            collapsePresent: !!this.collapse,
            togglerVisible: this.toggler ? this.isElementVisible(this.toggler) : false,
            dropdownCount: this.dropdowns.length
        };
        
        console.log('🧪 Test Responsive:', results);
        return results;
    }
    
    testDropdowns() {
        const results = [];
        
        this.dropdowns.forEach((dropdown, index) => {
            const menu = dropdown.nextElementSibling;
            const hasMenu = menu && menu.classList.contains('dropdown-menu');
            const hasBootstrapAttrs = dropdown.hasAttribute('data-bs-toggle');
            
            results.push({
                index: index + 1,
                hasMenu,
                hasBootstrapAttrs,
                text: dropdown.textContent.trim()
            });
        });
        
        console.log('🔽 Test Dropdowns:', results);
        return results;
    }
    
    testBootstrapIntegration() {
        const results = {
            bootstrapLoaded: typeof bootstrap !== 'undefined',
            jqueryLoaded: typeof $ !== 'undefined',
            navbarClasses: this.navbar ? this.navbar.className : null,
            togglerClasses: this.toggler ? this.toggler.className : null
        };
        
        console.log('🅱️ Test Bootstrap:', results);
        return results;
    }
    
    simulateMobile() {
        console.log('📱 Simulation mode mobile...');
        document.body.style.width = '375px';
        window.dispatchEvent(new Event('resize'));
        
        setTimeout(() => {
            console.log('📱 Mode mobile simulé');
            this.logNavbarInfo();
        }, 100);
    }
    
    simulateDesktop() {
        console.log('🖥️ Simulation mode desktop...');
        document.body.style.width = '100%';
        window.dispatchEvent(new Event('resize'));
        
        setTimeout(() => {
            console.log('🖥️ Mode desktop simulé');
            this.logNavbarInfo();
        }, 100);
    }
    
    isElementVisible(element) {
        const style = window.getComputedStyle(element);
        return style.display !== 'none' && style.visibility !== 'hidden';
    }
    
    runAllTests() {
        console.log('🚀 Lancement de tous les tests...');
        
        const responsive = this.testResponsive();
        const dropdowns = this.testDropdowns();
        const bootstrap = this.testBootstrapIntegration();
        
        const summary = {
            responsive,
            dropdowns,
            bootstrap,
            timestamp: new Date().toISOString()
        };
        
        console.log('📋 Résumé des tests:', summary);
        return summary;
    }
}

// Initialisation automatique en mode debug
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', () => {
        window.navbarTester = new NavbarTester();
        
        // Commandes globales pour la console
        window.testNavbar = () => window.navbarTester.runAllTests();
        window.testResponsive = () => window.navbarTester.testResponsive();
        window.testDropdowns = () => window.navbarTester.testDropdowns();
        window.simulateMobile = () => window.navbarTester.simulateMobile();
        window.simulateDesktop = () => window.navbarTester.simulateDesktop();
        
        console.log('🎯 Commandes disponibles dans la console:');
        console.log('- testNavbar() : Lance tous les tests');
        console.log('- testResponsive() : Test responsive');
        console.log('- testDropdowns() : Test dropdowns');
        console.log('- simulateMobile() : Simule mobile');
        console.log('- simulateDesktop() : Simule desktop');
    });
}
