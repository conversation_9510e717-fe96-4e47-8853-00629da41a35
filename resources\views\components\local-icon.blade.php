@props([
    'type',
    'size' => 'md'
])

@php
    $typeConfig = [
        'sport' => [
            'icon' => 'fas fa-futbol',
            'color' => 'text-success',
            'label' => 'Sport'
        ],
        'conference' => [
            'icon' => 'fas fa-presentation-screen',
            'color' => 'text-primary',
            'label' => 'Conférence'
        ],
        'fête' => [
            'icon' => 'fas fa-glass-cheers',
            'color' => 'text-warning',
            'label' => 'Fête'
        ]
    ];
    
    $sizeClasses = [
        'sm' => '',
        'md' => 'fa-lg',
        'lg' => 'fa-2x',
        'xl' => 'fa-3x'
    ];
    
    $config = $typeConfig[$type] ?? [
        'icon' => 'fas fa-building',
        'color' => 'text-muted',
        'label' => ucfirst($type)
    ];
    
    $sizeClass = $sizeClasses[$size] ?? 'fa-lg';
@endphp

<i class="{{ $config['icon'] }} {{ $config['color'] }} {{ $sizeClass }}" 
   title="{{ $config['label'] }}" 
   {{ $attributes }}></i>
