@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <!-- Hero Section -->
            <div class="text-center mb-5">
                <div class="hero-icon mb-3">
                    <i class="fas fa-network-wired fa-4x text-success"></i>
                </div>
                <h1 class="display-5 fw-bold text-success mb-3">Accès Réseau LocaSpace</h1>
                <p class="lead text-muted">
                    Votre application est maintenant accessible sur le réseau local
                </p>
            </div>

            <!-- Informations de connexion -->
            <div class="row g-4 mb-5">
                <!-- URL principale -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-lg h-100">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-globe fa-3x text-primary"></i>
                            </div>
                            <h4 class="card-title mb-3">URL Principale</h4>
                            <div class="url-display p-3 bg-light rounded mb-3">
                                <code class="fs-5 text-primary">{{ request()->getSchemeAndHttpHost() }}</code>
                            </div>
                            <button class="btn btn-primary" onclick="copyToClipboard('{{ request()->getSchemeAndHttpHost() }}')">
                                <i class="fas fa-copy me-2"></i>Copier l'URL
                            </button>
                        </div>
                    </div>
                </div>

                <!-- QR Code pour accès mobile -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-lg h-100">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-qrcode fa-3x text-warning"></i>
                            </div>
                            <h4 class="card-title mb-3">QR Code d'accès</h4>
                            <div id="network-qr-code" class="mb-3">
                                <!-- QR code sera généré ici -->
                            </div>
                            <button class="btn btn-warning" onclick="generateNetworkQR()">
                                <i class="fas fa-qrcode me-2"></i>Générer QR Code
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pages disponibles -->
            <div class="card border-0 shadow-lg mb-5">
                <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);">
                    <h5 class="mb-0">
                        <i class="fas fa-sitemap me-2"></i>Pages disponibles
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6 col-lg-4">
                            <div class="page-link-card p-3 border rounded">
                                <h6><i class="fas fa-home me-2 text-primary"></i>Accueil</h6>
                                <a href="{{ route('home') }}" class="btn btn-sm btn-outline-primary w-100" target="_blank">
                                    Ouvrir
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="page-link-card p-3 border rounded">
                                <h6><i class="fas fa-qrcode me-2 text-warning"></i>QR Code</h6>
                                <a href="{{ route('qr.login') }}" class="btn btn-sm btn-outline-warning w-100" target="_blank">
                                    Ouvrir
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="page-link-card p-3 border rounded">
                                <h6><i class="fas fa-building me-2 text-info"></i>Locaux</h6>
                                <a href="{{ route('locals.index') }}" class="btn btn-sm btn-outline-info w-100" target="_blank">
                                    Ouvrir
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="page-link-card p-3 border rounded">
                                <h6><i class="fas fa-user me-2 text-success"></i>Profil</h6>
                                <a href="{{ route('profile.show') }}" class="btn btn-sm btn-outline-success w-100" target="_blank">
                                    Ouvrir
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="page-link-card p-3 border rounded">
                                <h6><i class="fas fa-sign-in-alt me-2 text-secondary"></i>Connexion</h6>
                                <a href="{{ route('login') }}" class="btn btn-sm btn-outline-secondary w-100" target="_blank">
                                    Ouvrir
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="page-link-card p-3 border rounded">
                                <h6><i class="fas fa-user-plus me-2 text-dark"></i>Inscription</h6>
                                <a href="{{ route('register') }}" class="btn btn-sm btn-outline-dark w-100" target="_blank">
                                    Ouvrir
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations de connexion -->
            <div class="card border-0 shadow-lg mb-5">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-key me-2"></i>Informations de connexion
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Compte Administrateur :</h6>
                            <ul class="list-unstyled">
                                <li><strong>Email :</strong> <code><EMAIL></code></li>
                                <li><strong>Mot de passe :</strong> <code>password</code></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Compte Utilisateur :</h6>
                            <ul class="list-unstyled">
                                <li><strong>Email :</strong> <code><EMAIL></code></li>
                                <li><strong>Mot de passe :</strong> <code>password</code></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="card border-0 bg-light">
                <div class="card-body p-4">
                    <h5 class="card-title">
                        <i class="fas fa-info-circle me-2 text-info"></i>
                        Comment accéder depuis d'autres appareils
                    </h5>
                    <div class="row g-3 mt-2">
                        <div class="col-md-4">
                            <div class="d-flex align-items-start">
                                <div class="step-number me-3">1</div>
                                <div>
                                    <h6>Même réseau WiFi</h6>
                                    <small class="text-muted">Assurez-vous que vos appareils sont sur le même réseau WiFi</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-start">
                                <div class="step-number me-3">2</div>
                                <div>
                                    <h6>Ouvrir navigateur</h6>
                                    <small class="text-muted">Tapez l'URL ou scannez le QR code</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-start">
                                <div class="step-number me-3">3</div>
                                <div>
                                    <h6>Profiter !</h6>
                                    <small class="text-muted">Utilisez toutes les fonctionnalités de LocaSpace</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Copier l'URL dans le presse-papiers
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('URL copiée dans le presse-papiers !', 'success');
    }, function(err) {
        console.error('Erreur lors de la copie: ', err);
        showNotification('Erreur lors de la copie', 'error');
    });
}

// Générer le QR code pour l'accès réseau
function generateNetworkQR() {
    const url = '{{ request()->getSchemeAndHttpHost() }}';
    const qrContainer = document.getElementById('network-qr-code');
    
    // Vider le conteneur
    qrContainer.innerHTML = '';
    
    // Générer le QR code
    QRCode.toCanvas(qrContainer, url, {
        width: 200,
        height: 200,
        colorDark: '#4f46e5',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
    }, function (error) {
        if (error) {
            console.error(error);
            qrContainer.innerHTML = '<p class="text-danger">Erreur lors de la génération du QR code</p>';
        } else {
            console.log('QR code généré avec succès');
            
            // Ajouter un bouton de téléchargement
            const downloadBtn = document.createElement('button');
            downloadBtn.className = 'btn btn-sm btn-outline-primary mt-2';
            downloadBtn.innerHTML = '<i class="fas fa-download me-1"></i>Télécharger';
            downloadBtn.onclick = function() {
                const canvas = qrContainer.querySelector('canvas');
                const link = document.createElement('a');
                link.download = 'locaspace-network-qr.png';
                link.href = canvas.toDataURL();
                link.click();
            };
            qrContainer.appendChild(downloadBtn);
        }
    });
}

// Afficher une notification
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Générer automatiquement le QR code au chargement
document.addEventListener('DOMContentLoaded', function() {
    generateNetworkQR();
});
</script>

<style>
.hero-icon { animation: float 3s ease-in-out infinite; }
@keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }
.feature-icon { animation: pulse 2s ease-in-out infinite; }
@keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }

.step-number {
    width: 30px; height: 30px; background: linear-gradient(135deg, #4f46e5, #3730a3);
    color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center;
    font-weight: bold; font-size: 0.875rem; flex-shrink: 0;
}

.url-display {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    border: 2px dashed #cbd5e1;
}

.page-link-card {
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.page-link-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card:hover { transform: translateY(-2px); transition: all 0.3s ease; }
</style>
@endpush
