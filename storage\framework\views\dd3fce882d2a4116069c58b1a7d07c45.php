<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Accueil</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('reservations.index')); ?>">Mes réservations</a></li>
            <li class="breadcrumb-item active">Réservation #<?php echo e($reservation->id); ?></li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-calendar-check me-2"></i>Détails de la réservation
                        </h4>
                        <div>
                            <?php if($reservation->status === 'confirmée'): ?>
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-check me-1"></i>Confirmée
                                </span>
                            <?php elseif($reservation->status === 'en attente'): ?>
                                <span class="badge bg-warning fs-6">
                                    <i class="fas fa-clock me-1"></i>En attente
                                </span>
                            <?php else: ?>
                                <span class="badge bg-danger fs-6">
                                    <i class="fas fa-times me-1"></i>Annulée
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Reservation Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5><i class="fas fa-info-circle text-primary me-2"></i>Informations de réservation</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Numéro :</strong></td>
                                    <td>#<?php echo e($reservation->id); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Date :</strong></td>
                                    <td><?php echo e($reservation->date->format('d/m/Y')); ?> (<?php echo e($reservation->date->format('l')); ?>)</td>
                                </tr>
                                <tr>
                                    <td><strong>Heure :</strong></td>
                                    <td><?php echo e($reservation->start_time); ?> - <?php echo e($reservation->end_time); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Durée :</strong></td>
                                    <td><?php echo e($reservation->getDurationInHours()); ?> heure(s)</td>
                                </tr>
                                <tr>
                                    <td><strong>Créée le :</strong></td>
                                    <td><?php echo e($reservation->created_at->format('d/m/Y à H:i')); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-building text-primary me-2"></i>Local réservé</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <?php if($reservation->local->type === 'sport'): ?>
                                            <i class="fas fa-futbol text-success fa-2x me-3"></i>
                                        <?php elseif($reservation->local->type === 'conference'): ?>
                                            <i class="fas fa-presentation-screen text-primary fa-2x me-3"></i>
                                        <?php else: ?>
                                            <i class="fas fa-glass-cheers text-warning fa-2x me-3"></i>
                                        <?php endif; ?>
                                        <div>
                                            <h6 class="mb-0"><?php echo e($reservation->local->name); ?></h6>
                                            <small class="text-muted"><?php echo e(ucfirst($reservation->local->type)); ?></small>
                                        </div>
                                    </div>
                                    <p class="mb-2">
                                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                        <?php echo e($reservation->local->location); ?>

                                    </p>
                                    <p class="mb-2">
                                        <i class="fas fa-users text-muted me-2"></i>
                                        Capacité : <?php echo e($reservation->local->capacity); ?> personnes
                                    </p>
                                    <p class="mb-0">
                                        <i class="fas fa-euro-sign text-muted me-2"></i>
                                        Prix : <?php echo e($reservation->local->price); ?>€/heure
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Equipment -->
                    <?php if($reservation->local->equipment && count($reservation->local->equipment) > 0): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5><i class="fas fa-cogs text-primary me-2"></i>Équipements inclus</h5>
                            <div class="d-flex flex-wrap gap-2">
                                <?php $__currentLoopData = $reservation->local->equipment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equipment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="badge bg-light text-dark border">
                                        <?php if($equipment === 'wifi'): ?>
                                            <i class="fas fa-wifi me-1"></i>WiFi
                                        <?php elseif($equipment === 'projecteur'): ?>
                                            <i class="fas fa-video me-1"></i>Projecteur
                                        <?php elseif($equipment === 'climatisation'): ?>
                                            <i class="fas fa-snowflake me-1"></i>Climatisation
                                        <?php else: ?>
                                            <i class="fas fa-check me-1"></i><?php echo e($equipment); ?>

                                        <?php endif; ?>
                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="<?php echo e(route('reservations.index')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Retour à mes réservations
                                </a>
                                
                                <div>
                                    <?php if(in_array($reservation->status, ['en attente', 'confirmée'])): ?>
                                        <form method="POST" action="<?php echo e(route('reservations.cancel', $reservation)); ?>" 
                                              style="display: inline;">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PATCH'); ?>
                                            <button type="submit" 
                                                    class="btn btn-outline-danger"
                                                    onclick="return confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')">
                                                <i class="fas fa-times me-2"></i>Annuler la réservation
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Invoice Card -->
            <?php if($reservation->invoice): ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-invoice me-2"></i>Facture
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <h4 class="text-success"><?php echo e($reservation->invoice->amount); ?>€</h4>
                        <p class="text-muted mb-0">Montant total</p>
                    </div>

                    <div class="mb-3">
                        <strong>Statut du paiement :</strong><br>
                        <?php if($reservation->invoice->isPaid()): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Payé
                            </span>
                        <?php else: ?>
                            <span class="badge bg-danger">
                                <i class="fas fa-exclamation-circle me-1"></i>Non payé
                            </span>
                        <?php endif; ?>
                    </div>

                    <div class="d-grid gap-2">
                        <?php if($reservation->invoice->isPaid()): ?>
                            <a href="<?php echo e(route('invoices.show', $reservation->invoice)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>Voir la facture
                            </a>
                            <a href="<?php echo e(route('invoices.download', $reservation->invoice)); ?>" class="btn btn-success">
                                <i class="fas fa-download me-2"></i>Télécharger PDF
                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(route('invoices.show', $reservation->invoice)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>Voir la facture
                            </a>
                            <form method="POST" action="<?php echo e(route('invoices.pay', $reservation->invoice)); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-credit-card me-2"></i>Payer maintenant
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Contact Support -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-headset me-2"></i>Besoin d'aide ?
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        Une question concernant votre réservation ? Notre équipe est là pour vous aider.
                    </p>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>Contacter le support
                        </button>
                    </div>
                </div>
            </div>

            <!-- Reservation Timeline -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Historique
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Réservation créée</h6>
                                <small class="text-muted"><?php echo e($reservation->created_at->format('d/m/Y à H:i')); ?></small>
                            </div>
                        </div>
                        
                        <?php if($reservation->status === 'confirmée'): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Réservation confirmée</h6>
                                <small class="text-muted"><?php echo e($reservation->updated_at->format('d/m/Y à H:i')); ?></small>
                            </div>
                        </div>
                        <?php elseif($reservation->status === 'annulée'): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Réservation annulée</h6>
                                <small class="text-muted"><?php echo e($reservation->updated_at->format('d/m/Y à H:i')); ?></small>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #dee2e6;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\hackathon\locaspace\resources\views/reservations/show.blade.php ENDPATH**/ ?>