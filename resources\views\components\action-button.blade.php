@props([
    'href' => null,
    'type' => 'button',
    'variant' => 'primary',
    'size' => 'md',
    'icon' => null,
    'loading' => false,
    'disabled' => false,
    'outline' => false
])

@php
    $sizeClasses = [
        'sm' => 'btn-sm',
        'md' => '',
        'lg' => 'btn-lg'
    ];
    
    $variantPrefix = $outline ? 'btn-outline-' : 'btn-';
    $btnClass = $variantPrefix . $variant;
    $sizeClass = $sizeClasses[$size] ?? '';
@endphp

@if($href)
    <a href="{{ $href }}" 
       class="btn {{ $btnClass }} {{ $sizeClass }} {{ $disabled ? 'disabled' : '' }}" 
       {{ $attributes }}>
        @if($loading)
            <i class="fas fa-spinner fa-spin me-2"></i>
        @elseif($icon)
            <i class="{{ $icon }} me-2"></i>
        @endif
        {{ $slot }}
    </a>
@else
    <button type="{{ $type }}" 
            class="btn {{ $btnClass }} {{ $sizeClass }}" 
            {{ $disabled ? 'disabled' : '' }}
            {{ $attributes }}>
        @if($loading)
            <i class="fas fa-spinner fa-spin me-2"></i>
        @elseif($icon)
            <i class="{{ $icon }} me-2"></i>
        @endif
        {{ $slot }}
    </button>
@endif
