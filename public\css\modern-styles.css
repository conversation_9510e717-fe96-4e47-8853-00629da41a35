/* ===== STYLES MODERNES LOCASPACE ===== */

/* Variables CSS étendues */
:root {
    /* Couleurs principales cohérentes */
    --primary-color: #4f46e5;
    --primary-light: #6366f1;
    --primary-dark: #3730a3;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;

    /* Gradients cohérents */
    --primary-gradient: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --info-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --light-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

    /* Couleurs de fond */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-sm: 8px;
    --spacing: 1.5rem;
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    --glass-bg: rgba(255, 255, 255, 0.9);
    --glass-border: rgba(255, 255, 255, 0.2);
    --backdrop-blur: blur(10px);
}

/* Body et Layout Global */
body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f5f9 100%) !important;
    min-height: 100vh;
    line-height: 1.6;
    color: var(--text-primary) !important;
}

.main-content {
    min-height: calc(100vh - 70px);
    padding-top: 2rem;
}

/* Container amélioré */
.container {
    max-width: 1200px;
}

/* Cards modernes */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    backdrop-filter: var(--backdrop-blur);
    background: rgba(255, 255, 255, 0.95);
    transition: var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: rgba(248, 249, 250, 0.5);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
}

/* Boutons modernes */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition-normal);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

.btn-success {
    background: var(--success-gradient);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-warning {
    background: var(--warning-gradient);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-danger {
    background: var(--danger-gradient);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-info {
    background: var(--info-gradient);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.btn-outline-primary {
    border: 2px solid #007bff;
    color: #007bff;
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    transform: translateY(-2px);
}

/* Forms modernes */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    transition: var(--transition-fast);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: var(--backdrop-blur);
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    background: rgba(255, 255, 255, 1);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-select {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius-sm);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: var(--backdrop-blur);
}

/* Alerts modernes */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: var(--backdrop-blur);
    border-left: 4px solid;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: #28a745;
    color: #155724;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
    color: #721c24;
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: #ffc107;
    color: #856404;
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    border-left-color: #17a2b8;
    color: #0c5460;
}

/* Badges modernes */
.badge {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
}

.badge.bg-success {
    background: var(--success-gradient) !important;
}

.badge.bg-warning {
    background: var(--warning-gradient) !important;
}

.badge.bg-danger {
    background: var(--danger-gradient) !important;
}

.badge.bg-primary {
    background: var(--primary-gradient) !important;
}

/* Tables modernes */
.table {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: var(--backdrop-blur);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: rgba(0, 123, 255, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Breadcrumb moderne */
.breadcrumb {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: var(--backdrop-blur);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    font-weight: bold;
    color: #6c757d;
}

/* Pagination moderne */
.pagination .page-link {
    border: none;
    border-radius: var(--border-radius-sm);
    margin: 0 0.25rem;
    padding: 0.75rem 1rem;
    transition: var(--transition-fast);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: var(--backdrop-blur);
}

.pagination .page-link:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: var(--primary-gradient);
    border: none;
}

/* Progress bars */
.progress {
    height: 8px;
    border-radius: var(--border-radius-sm);
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.progress-bar {
    background: var(--primary-gradient);
    transition: var(--transition-normal);
}

/* Modals modernes */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    backdrop-filter: var(--backdrop-blur);
    background: rgba(255, 255, 255, 0.95);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
}

/* Tooltips */
.tooltip .tooltip-inner {
    background: var(--dark-gradient);
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-100px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
    .main-content {
        padding-top: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

/* Light theme enforced - styles moved to main selectors above */
