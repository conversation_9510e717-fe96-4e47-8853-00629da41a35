<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'LocaSpace') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .navbar-nav .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0.375rem;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.5rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }

        .dropdown-header {
            font-weight: 600;
            color: #6c757d;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .dropdown-item-title {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .dropdown-item-text {
            font-size: 0.75rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.25);
        }

        .sidebar {
            min-height: calc(100vh - 76px);
            background-color: #f8f9fa;
        }

        .main-content {
            min-height: calc(100vh - 76px);
        }

        .badge {
            font-size: 0.6rem;
        }

        .notification-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Responsive improvements */
        @media (max-width: 991.98px) {
            .navbar-nav .dropdown-menu {
                border: 1px solid rgba(0, 0, 0, 0.15);
                margin-top: 0.5rem;
            }

            .dropdown-item:hover {
                transform: none;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
            <div class="container">
                <!-- Brand -->
                <a class="navbar-brand fw-bold" href="{{ route('home') }}">
                    <i class="fas fa-building me-2"></i>LocaSpace
                </a>

                <!-- Mobile Toggle -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <!-- Left Menu -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">
                                <i class="fas fa-home me-1"></i>Accueil
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('locals.*') ? 'active' : '' }}" href="{{ route('locals.index') }}">
                                <i class="fas fa-search me-1"></i>Parcourir les locaux
                            </a>
                        </li>
                        @auth
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-1"></i>Tableau de bord
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle {{ request()->routeIs('reservations.*') ? 'active' : '' }}" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-calendar-alt me-1"></i>Réservations
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{{ route('reservations.index') }}">
                                        <i class="fas fa-list me-2"></i>Mes réservations
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ route('locals.index') }}">
                                        <i class="fas fa-plus me-2"></i>Nouvelle réservation
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ route('reservations.index', ['status' => 'en attente']) }}">
                                        <i class="fas fa-clock me-2 text-warning"></i>En attente
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ route('reservations.index', ['status' => 'confirmée']) }}">
                                        <i class="fas fa-check me-2 text-success"></i>Confirmées
                                    </a></li>
                                </ul>
                            </li>
                            @if(Auth::user()->isAdmin())
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.*') ? 'active' : '' }}" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-cogs me-1"></i>Administration
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><h6 class="dropdown-header">Tableau de bord</h6></li>
                                        <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}">
                                            <i class="fas fa-chart-bar me-2"></i>Dashboard Admin
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><h6 class="dropdown-header">Gestion</h6></li>
                                        <li><a class="dropdown-item" href="{{ route('admin.locals.index') }}">
                                            <i class="fas fa-building me-2"></i>Gestion Locaux
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('admin.reservations.index') }}">
                                            <i class="fas fa-calendar-check me-2"></i>Réservations
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('admin.users.index') }}">
                                            <i class="fas fa-users me-2"></i>Utilisateurs
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('admin.invoices.index') }}">
                                            <i class="fas fa-file-invoice me-2"></i>Factures
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="{{ route('admin.reports') }}">
                                            <i class="fas fa-chart-line me-2"></i>Rapports & Analytics
                                        </a></li>
                                    </ul>
                                </li>
                            @endif
                        @endauth
                    </ul>

                    <!-- Right Menu -->
                    <ul class="navbar-nav">
                        @guest
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('qr.login') }}" title="Connexion rapide par QR Code">
                                    <i class="fas fa-qrcode me-1"></i>QR Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('login') }}">
                                    <i class="fas fa-sign-in-alt me-1"></i>Connexion
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="btn btn-outline-light btn-sm ms-2" href="{{ route('register') }}">
                                    <i class="fas fa-user-plus me-1"></i>Inscription
                                </a>
                            </li>
                        @else
                            <!-- Notifications -->
                            <li class="nav-item dropdown">
                                <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-bell"></i>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge">
                                        3
                                        <span class="visually-hidden">notifications non lues</span>
                                    </span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                                    <li><h6 class="dropdown-header">Notifications</h6></li>
                                    <li><a class="dropdown-item" href="#">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-check-circle text-success"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-2">
                                                <h6 class="dropdown-item-title">Réservation confirmée</h6>
                                                <p class="dropdown-item-text">Votre réservation du 15/12 a été confirmée</p>
                                                <small class="text-muted">Il y a 2 heures</small>
                                            </div>
                                        </div>
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-center" href="#">
                                        <small>Voir toutes les notifications</small>
                                    </a></li>
                                </ul>
                            </li>

                            <!-- User Menu -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="bg-light rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                        <i class="fas fa-user text-primary"></i>
                                    </div>
                                    <span class="d-none d-md-inline">{{ Auth::user()->name }}</span>
                                    @if(Auth::user()->isAdmin())
                                        <span class="badge bg-danger ms-1">Admin</span>
                                    @endif
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><h6 class="dropdown-header">Mon compte</h6></li>
                                    <li><a class="dropdown-item" href="{{ route('profile.show') }}">
                                        <i class="fas fa-user me-2"></i>Mon profil
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ route('reservations.index') }}">
                                        <i class="fas fa-calendar-alt me-2"></i>Mes réservations
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header">Paramètres</h6></li>
                                    <li><a class="dropdown-item" href="#">
                                        <i class="fas fa-cog me-2"></i>Préférences
                                    </a></li>
                                    <li><a class="dropdown-item" href="#">
                                        <i class="fas fa-question-circle me-2"></i>Aide & Support
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Flash Messages -->
            @if(session('success'))
                <div class="container mt-3">
                    <x-alert type="success">
                        {{ session('success') }}
                    </x-alert>
                </div>
            @endif

            @if(session('error'))
                <div class="container mt-3">
                    <x-alert type="error">
                        {{ session('error') }}
                    </x-alert>
                </div>
            @endif

            @if(session('warning'))
                <div class="container mt-3">
                    <x-alert type="warning">
                        {{ session('warning') }}
                    </x-alert>
                </div>
            @endif

            @if(session('info'))
                <div class="container mt-3">
                    <x-alert type="info">
                        {{ session('info') }}
                    </x-alert>
                </div>
            @endif

            @yield('content')
        </main>

        <!-- Footer -->
        <footer class="bg-dark text-light py-4 mt-5">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 mb-3">
                        <h5 class="fw-bold">
                            <i class="fas fa-building me-2"></i>LocaSpace
                        </h5>
                        <p class="text-muted">
                            La plateforme de référence pour la réservation d'espaces au Maroc.
                            Trouvez et réservez facilement le local parfait pour vos événements.
                        </p>
                        <div class="d-flex gap-3">
                            <a href="#" class="text-light">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="text-light">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="text-light">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="text-light">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-3">
                        <h6 class="fw-bold">Navigation</h6>
                        <ul class="list-unstyled">
                            <li><a href="{{ route('home') }}" class="text-muted text-decoration-none">Accueil</a></li>
                            <li><a href="{{ route('locals.index') }}" class="text-muted text-decoration-none">Locaux</a></li>
                            @auth
                                <li><a href="{{ route('dashboard') }}" class="text-muted text-decoration-none">Dashboard</a></li>
                                <li><a href="{{ route('reservations.index') }}" class="text-muted text-decoration-none">Réservations</a></li>
                            @else
                                <li><a href="{{ route('login') }}" class="text-muted text-decoration-none">Connexion</a></li>
                                <li><a href="{{ route('register') }}" class="text-muted text-decoration-none">Inscription</a></li>
                            @endauth
                        </ul>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-3">
                        <h6 class="fw-bold">Types de locaux</h6>
                        <ul class="list-unstyled">
                            <li><a href="{{ route('locals.index', ['type' => 'sport']) }}" class="text-muted text-decoration-none">Terrains de sport</a></li>
                            <li><a href="{{ route('locals.index', ['type' => 'conference']) }}" class="text-muted text-decoration-none">Salles de conférence</a></li>
                            <li><a href="{{ route('locals.index', ['type' => 'fête']) }}" class="text-muted text-decoration-none">Salles de fête</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-3">
                        <h6 class="fw-bold">Support</h6>
                        <ul class="list-unstyled">
                            <li><a href="#" class="text-muted text-decoration-none">Centre d'aide</a></li>
                            <li><a href="#" class="text-muted text-decoration-none">Contact</a></li>
                            <li><a href="#" class="text-muted text-decoration-none">FAQ</a></li>
                            <li><a href="#" class="text-muted text-decoration-none">Conditions d'utilisation</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-3">
                        <h6 class="fw-bold">Contact</h6>
                        <ul class="list-unstyled text-muted">
                            <li><i class="fas fa-map-marker-alt me-2"></i>Casablanca, Maroc</li>
                            <li><i class="fas fa-phone me-2"></i>+212 5 22 XX XX XX</li>
                            <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        </ul>
                    </div>
                </div>
                <hr class="my-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="text-muted mb-0">
                            &copy; {{ date('Y') }} LocaSpace. Tous droits réservés.
                        </p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small class="text-muted">
                            Développé avec <i class="fas fa-heart text-danger"></i> par l'équipe LocaSpace
                        </small>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading state to forms
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Chargement...';

                    // Re-enable after 10 seconds as fallback
                    setTimeout(() => {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    }, 10000);
                }
            });
        });

        // Tooltip initialization
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Popover initialization
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    </script>

    @stack('scripts')
</body>
</html>
