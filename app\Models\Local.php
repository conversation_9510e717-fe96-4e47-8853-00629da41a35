<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Local extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'location',
        'capacity',
        'price',
        'equipment',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'equipment' => 'array',
            'status' => 'boolean',
            'price' => 'decimal:2',
        ];
    }

    /**
     * Get the reservations for the local.
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Check if local is available for a specific date and time range.
     */
    public function isAvailable($date, $startTime, $endTime, $excludeReservationId = null): bool
    {
        $query = $this->reservations()
            ->where('date', $date)
            ->where('status', '!=', 'annulée')
            ->where(function ($q) use ($startTime, $endTime) {
                $q->whereBetween('start_time', [$startTime, $endTime])
                  ->orWhereBetween('end_time', [$startTime, $endTime])
                  ->orWhere(function ($q2) use ($startTime, $endTime) {
                      $q2->where('start_time', '<=', $startTime)
                         ->where('end_time', '>=', $endTime);
                  });
            });

        if ($excludeReservationId) {
            $query->where('id', '!=', $excludeReservationId);
        }

        return $query->count() === 0;
    }

    /**
     * Scope for active locals.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope for filtering by type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }
}
