<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Hero Section -->
            <div class="text-center mb-5">
                <div class="hero-icon mb-3">
                    <i class="fas fa-camera fa-4x text-primary"></i>
                </div>
                <h1 class="display-5 fw-bold text-primary mb-3">Test Caméra & Permissions</h1>
                <p class="lead text-muted">
                    Testez l'accès à votre caméra et les permissions pour le scanner QR
                </p>
            </div>

            <!-- État des permissions -->
            <div class="card border-0 shadow-lg mb-4">
                <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>État des permissions
                    </h5>
                </div>
                <div class="card-body">
                    <div id="permission-status" class="permission-status">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm text-primary me-3" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <span>Vérification des permissions en cours...</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tests disponibles -->
            <div class="row g-4 mb-5">
                <!-- Test permissions -->
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-key fa-3x text-warning"></i>
                            </div>
                            <h4 class="card-title mb-3">Vérifier Permissions</h4>
                            <p class="card-text text-muted mb-4">
                                Vérifiez l'état des permissions caméra de votre navigateur
                            </p>
                            <button class="btn btn-warning btn-lg" onclick="checkPermissions()">
                                <i class="fas fa-search me-2"></i>Vérifier
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Test caméra -->
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-video fa-3x text-success"></i>
                            </div>
                            <h4 class="card-title mb-3">Test Caméra</h4>
                            <p class="card-text text-muted mb-4">
                                Testez l'accès direct à votre caméra
                            </p>
                            <button class="btn btn-success btn-lg" onclick="testCamera()">
                                <i class="fas fa-camera me-2"></i>Tester
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Zone de test caméra -->
            <div class="card border-0 shadow-lg mb-4" id="camera-test-area" style="display: none;">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-video me-2"></i>Aperçu Caméra
                    </h5>
                </div>
                <div class="card-body text-center">
                    <video id="test-video" autoplay muted playsinline style="max-width: 100%; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);"></video>
                    <div class="mt-3">
                        <button class="btn btn-danger" onclick="stopCamera()">
                            <i class="fas fa-stop me-2"></i>Arrêter
                        </button>
                        <button class="btn btn-primary" onclick="switchTestCamera()">
                            <i class="fas fa-sync-alt me-2"></i>Changer caméra
                        </button>
                    </div>
                </div>
            </div>

            <!-- Test QR Scanner -->
            <div class="card border-0 shadow-lg mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-qrcode me-2"></i>Test Scanner QR
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="text-muted mb-4">
                        Testez le scanner QR complet avec gestion des permissions
                    </p>
                    <button class="btn btn-info btn-lg" data-qr-scan>
                        <i class="fas fa-qrcode me-2"></i>Démarrer Scanner QR
                    </button>
                </div>
            </div>

            <!-- Informations navigateur -->
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-info-circle me-2 text-info"></i>
                        Informations Navigateur
                    </h5>
                    <div id="browser-info" class="mt-3">
                        <!-- Sera rempli par JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let testStream = null;
let currentFacingMode = 'environment';

// Vérifier les permissions au chargement
document.addEventListener('DOMContentLoaded', function() {
    checkPermissions();
    displayBrowserInfo();
});

// Vérifier les permissions caméra
async function checkPermissions() {
    const statusElement = document.getElementById('permission-status');
    
    try {
        // Vérifier le support des API
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            statusElement.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Non supporté :</strong> Votre navigateur ne supporte pas l'accès à la caméra
                </div>
            `;
            return;
        }

        // Vérifier les permissions avec l'API Permissions
        if (navigator.permissions) {
            const permission = await navigator.permissions.query({ name: 'camera' });
            
            let statusClass, statusIcon, statusText;
            
            switch (permission.state) {
                case 'granted':
                    statusClass = 'alert-success';
                    statusIcon = 'check-circle';
                    statusText = 'Autorisé : L\'accès à la caméra est autorisé';
                    break;
                case 'denied':
                    statusClass = 'alert-danger';
                    statusIcon = 'times-circle';
                    statusText = 'Refusé : L\'accès à la caméra a été refusé';
                    break;
                case 'prompt':
                    statusClass = 'alert-warning';
                    statusIcon = 'question-circle';
                    statusText = 'En attente : L\'autorisation sera demandée lors de l\'utilisation';
                    break;
                default:
                    statusClass = 'alert-info';
                    statusIcon = 'info-circle';
                    statusText = 'Inconnu : État des permissions non déterminé';
            }
            
            statusElement.innerHTML = `
                <div class="alert ${statusClass}">
                    <i class="fas fa-${statusIcon} me-2"></i>
                    <strong>${statusText}</strong>
                </div>
            `;
            
            // Écouter les changements de permissions
            permission.addEventListener('change', function() {
                checkPermissions();
            });
            
        } else {
            statusElement.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>API Permissions non disponible :</strong> Impossible de vérifier l'état des permissions
                </div>
            `;
        }
        
    } catch (error) {
        console.error('Erreur lors de la vérification des permissions:', error);
        statusElement.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Erreur :</strong> ${error.message}
            </div>
        `;
    }
}

// Tester l'accès caméra
async function testCamera() {
    try {
        // Arrêter le stream existant
        if (testStream) {
            testStream.getTracks().forEach(track => track.stop());
        }
        
        // Demander l'accès caméra
        testStream = await navigator.mediaDevices.getUserMedia({
            video: { 
                facingMode: currentFacingMode,
                width: { ideal: 1280 },
                height: { ideal: 720 }
            }
        });
        
        // Afficher la vidéo
        const video = document.getElementById('test-video');
        video.srcObject = testStream;
        video.play();
        
        // Afficher la zone de test
        document.getElementById('camera-test-area').style.display = 'block';
        
        // Mettre à jour les permissions
        checkPermissions();
        
        showNotification('Caméra activée avec succès !', 'success');
        
    } catch (error) {
        console.error('Erreur caméra:', error);
        
        let errorMessage = 'Erreur inconnue';
        switch (error.name) {
            case 'NotAllowedError':
                errorMessage = 'Accès refusé. Autorisez l\'accès à la caméra.';
                break;
            case 'NotFoundError':
                errorMessage = 'Aucune caméra trouvée sur cet appareil.';
                break;
            case 'NotReadableError':
                errorMessage = 'Caméra déjà utilisée par une autre application.';
                break;
            case 'OverconstrainedError':
                errorMessage = 'Paramètres caméra non supportés.';
                break;
            case 'SecurityError':
                errorMessage = 'Accès bloqué pour des raisons de sécurité (HTTPS requis).';
                break;
        }
        
        showNotification(errorMessage, 'error');
    }
}

// Arrêter la caméra
function stopCamera() {
    if (testStream) {
        testStream.getTracks().forEach(track => track.stop());
        testStream = null;
    }
    
    document.getElementById('camera-test-area').style.display = 'none';
    showNotification('Caméra arrêtée', 'info');
}

// Changer de caméra
async function switchTestCamera() {
    currentFacingMode = currentFacingMode === 'environment' ? 'user' : 'environment';
    await testCamera();
}

// Afficher les informations du navigateur
function displayBrowserInfo() {
    const info = {
        'Navigateur': navigator.userAgent,
        'Plateforme': navigator.platform,
        'Langue': navigator.language,
        'Cookies activés': navigator.cookieEnabled ? 'Oui' : 'Non',
        'En ligne': navigator.onLine ? 'Oui' : 'Non',
        'HTTPS': location.protocol === 'https:' ? 'Oui' : 'Non',
        'MediaDevices': navigator.mediaDevices ? 'Supporté' : 'Non supporté',
        'getUserMedia': navigator.mediaDevices?.getUserMedia ? 'Supporté' : 'Non supporté',
        'Permissions API': navigator.permissions ? 'Supporté' : 'Non supporté'
    };
    
    let infoHTML = '<div class="row g-3">';
    
    Object.entries(info).forEach(([key, value]) => {
        const isGood = ['Oui', 'Supporté'].includes(value);
        const isBad = ['Non', 'Non supporté'].includes(value);
        const badgeClass = isGood ? 'bg-success' : isBad ? 'bg-danger' : 'bg-secondary';
        
        infoHTML += `
            <div class="col-md-6">
                <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                    <span class="fw-medium">${key}:</span>
                    <span class="badge ${badgeClass}">${value}</span>
                </div>
            </div>
        `;
    });
    
    infoHTML += '</div>';
    document.getElementById('browser-info').innerHTML = infoHTML;
}

// Afficher une notification
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 
                     type === 'error' ? 'alert-danger' : 
                     type === 'info' ? 'alert-info' : 'alert-warning';
    const icon = type === 'success' ? 'check-circle' : 
                type === 'error' ? 'exclamation-triangle' : 
                type === 'info' ? 'info-circle' : 'exclamation-triangle';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Nettoyer au déchargement de la page
window.addEventListener('beforeunload', function() {
    if (testStream) {
        testStream.getTracks().forEach(track => track.stop());
    }
});
</script>

<style>
.hero-icon { animation: float 3s ease-in-out infinite; }
@keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }
.feature-icon { animation: pulse 2s ease-in-out infinite; }
@keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }
.card:hover { transform: translateY(-3px); transition: all 0.3s ease; }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\hackathon\locaspace\resources\views/camera-test.blade.php ENDPATH**/ ?>