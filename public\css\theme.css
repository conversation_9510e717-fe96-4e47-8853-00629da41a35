/* ===== LOCASPACE THEME COHÉRENT ===== */

/* Variables de couleurs principales */
:root {
    /* Palette principale - Indigo moderne */
    --primary: #4f46e5;
    --primary-light: #6366f1;
    --primary-dark: #3730a3;
    --primary-50: #eef2ff;
    --primary-100: #e0e7ff;
    --primary-500: #6366f1;
    --primary-600: #4f46e5;
    --primary-700: #3730a3;
    
    /* Couleurs secondaires */
    --secondary: #64748b;
    --accent: #f59e0b;
    --accent-light: #fbbf24;
    --accent-dark: #d97706;
    
    /* Couleurs de statut */
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #06b6d4;
    
    /* Couleurs neutres */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Couleurs de texte */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;
    
    /* Couleurs de fond */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-overlay: rgba(15, 23, 42, 0.5);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 100%);
    --gradient-success: linear-gradient(135deg, var(--success) 0%, #059669 100%);
    --gradient-bg: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 50%, var(--gray-100) 100%);
    
    /* Ombres */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Rayons de bordure */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-2xl: 2rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: all 0.15s ease-in-out;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Espacement */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Typographie */
    --font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Application du thème global */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: var(--gradient-bg) !important;
    color: var(--text-primary) !important;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Classes utilitaires de couleurs */
.text-primary { color: var(--primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-success { color: var(--success) !important; }
.text-warning { color: var(--warning) !important; }
.text-danger { color: var(--danger) !important; }
.text-info { color: var(--info) !important; }

.bg-primary { background-color: var(--primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-success { background-color: var(--success) !important; }
.bg-warning { background-color: var(--warning) !important; }
.bg-danger { background-color: var(--danger) !important; }
.bg-info { background-color: var(--info) !important; }

.bg-gradient-primary { background: var(--gradient-primary) !important; }
.bg-gradient-accent { background: var(--gradient-accent) !important; }
.bg-gradient-success { background: var(--gradient-success) !important; }

/* Classes utilitaires d'ombres */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--shadow-2xl) !important; }

/* Classes utilitaires de bordures */
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded { border-radius: var(--radius) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* Classes utilitaires de transitions */
.transition { transition: var(--transition) !important; }
.transition-fast { transition: var(--transition-fast) !important; }
.transition-slow { transition: var(--transition-slow) !important; }

/* Surcharge Bootstrap avec le thème */
.btn-primary {
    background: var(--gradient-primary) !important;
    border: none !important;
    color: var(--white) !important;
    box-shadow: var(--shadow-md) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%) !important;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg) !important;
}

.btn-outline-primary {
    border: 2px solid var(--primary) !important;
    color: var(--primary) !important;
    background: transparent !important;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary) !important;
    color: var(--white) !important;
    transform: translateY(-1px);
}

.card {
    background: var(--bg-primary) !important;
    border: 1px solid var(--gray-200) !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow) !important;
    color: var(--text-primary) !important;
}

.card:hover {
    box-shadow: var(--shadow-lg) !important;
    transform: translateY(-2px);
}

.form-control {
    background: var(--bg-primary) !important;
    border: 2px solid var(--gray-200) !important;
    border-radius: var(--radius) !important;
    color: var(--text-primary) !important;
    transition: var(--transition) !important;
}

.form-control:focus {
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.15) !important;
    background: var(--white) !important;
}

.navbar-dark {
    background: var(--gradient-primary) !important;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1) !important;
    border-left: 4px solid var(--success) !important;
    color: #065f46 !important;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1) !important;
    border-left: 4px solid var(--danger) !important;
    color: #991b1b !important;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1) !important;
    border-left: 4px solid var(--warning) !important;
    color: #92400e !important;
}

.alert-info {
    background: rgba(6, 182, 212, 0.1) !important;
    border-left: 4px solid var(--info) !important;
    color: #155e75 !important;
}

/* Animations cohérentes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-fade-in { animation: fadeIn 0.3s ease-out; }
.animate-slide-up { animation: slideUp 0.4s ease-out; }
.animate-scale-in { animation: scaleIn 0.3s ease-out; }

/* Responsive */
@media (max-width: 768px) {
    :root {
        --spacing: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
    }
}

/* Mode sombre désactivé - thème clair forcé */
@media (prefers-color-scheme: dark) {
    body {
        background: var(--gradient-bg) !important;
        color: var(--text-primary) !important;
    }
}
