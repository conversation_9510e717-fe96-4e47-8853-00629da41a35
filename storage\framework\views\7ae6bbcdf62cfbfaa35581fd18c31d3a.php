<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Gestion des réservations
                    </h1>
                    <p class="text-muted">Gérez toutes les réservations de la plateforme</p>
                </div>
                <div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-2"></i>Filtrer
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="?status=all">Toutes</a></li>
                            <li><a class="dropdown-item" href="?status=en_attente">En attente</a></li>
                            <li><a class="dropdown-item" href="?status=confirmée">Confirmées</a></li>
                            <li><a class="dropdown-item" href="?status=annulée">Annulées</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($reservations->count()); ?></h4>
                            <small>Total réservations</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($reservations->where('status', 'en attente')->count()); ?></h4>
                            <small>En attente</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($reservations->where('status', 'confirmée')->count()); ?></h4>
                            <small>Confirmées</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($reservations->where('status', 'annulée')->count()); ?></h4>
                            <small>Annulées</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservations Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Liste des réservations
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Client</th>
                            <th>Local</th>
                            <th>Date</th>
                            <th>Heure</th>
                            <th>Montant</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $reservations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reservation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <strong>#<?php echo e($reservation->id); ?></strong>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-2" style="width: 24px; height: 24px; background: #007bff; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-user text-white" style="font-size: 10px;"></i>
                                    </div>
                                    <div>
                                        <strong><?php echo e($reservation->user->name); ?></strong><br>
                                        <small class="text-muted"><?php echo e($reservation->user->email); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <strong><?php echo e($reservation->local->name); ?></strong><br>
                                <small class="text-muted"><?php echo e($reservation->local->location); ?></small>
                            </td>
                            <td>
                                <strong><?php echo e($reservation->date->format('d/m/Y')); ?></strong><br>
                                <small class="text-muted"><?php echo e($reservation->date->format('l')); ?></small>
                            </td>
                            <td>
                                <?php echo e($reservation->start_time); ?> - <?php echo e($reservation->end_time); ?><br>
                                <small class="text-muted"><?php echo e($reservation->getDurationInHours()); ?>h</small>
                            </td>
                            <td>
                                <strong><?php echo e(abs($reservation->calculateAmount())); ?> MAD</strong>
                            </td>
                            <td>
                                <?php if($reservation->status === 'confirmée'): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Confirmée
                                    </span>
                                <?php elseif($reservation->status === 'en attente'): ?>
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>En attente
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times-circle me-1"></i>Annulée
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="<?php echo e(route('reservations.show', $reservation)); ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if($reservation->status === 'en attente'): ?>
                                    <form method="POST" action="<?php echo e(route('admin.reservations.confirm', $reservation)); ?>" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PATCH'); ?>
                                        <button type="submit" class="btn btn-outline-success" title="Confirmer">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-outline-danger" onclick="cancelReservation(<?php echo e($reservation->id); ?>)" title="Annuler">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Aucune réservation trouvée</p>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function cancelReservation(reservationId) {
    if (confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')) {
        fetch(`/admin/reservations/${reservationId}/cancel`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de l\'annulation');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'annulation');
        });
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\hackathon\locaspace\resources\views/admin/reservations.blade.php ENDPATH**/ ?>