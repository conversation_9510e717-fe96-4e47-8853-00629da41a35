/* ===== NAVBAR DEBUG - FORCE VISIBILITY ===== */

/* Force navbar visibility */
.navbar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1050 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%) !important;
}

/* Force navbar content visibility */
.navbar .container {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force navbar brand visibility */
.navbar-brand {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: white !important;
    text-decoration: none !important;
}

/* Force navbar toggler visibility */
.navbar-toggler {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Force navbar collapse visibility */
.navbar-collapse {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force navbar nav visibility */
.navbar-nav {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    list-style: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Force nav items visibility */
.navbar-nav .nav-item {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force nav links visibility */
.navbar-nav .nav-link {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    padding: 0.75rem 1rem !important;
    border-radius: 8px !important;
    margin: 0 0.25rem !important;
    align-items: center !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

/* Force nav link text visibility */
.navbar-nav .nav-link span {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: inherit !important;
}

/* Force nav link icons visibility */
.navbar-nav .nav-link i {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: inherit !important;
    margin-right: 0.5rem !important;
}

/* Force hover states */
.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.15) !important;
    color: rgba(255, 255, 255, 1) !important;
    transform: translateY(-2px) !important;
}

/* Force active states */
.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(255, 255, 255, 1) !important;
}

/* Force dropdown visibility */
.dropdown-menu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    background: white !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
    z-index: 1060 !important;
}

/* Hide dropdown by default, show on hover/click */
.dropdown-menu:not(.show) {
    display: none !important;
}

.dropdown-menu.show {
    display: block !important;
}

/* Force dropdown items visibility */
.dropdown-item {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #333 !important;
    text-decoration: none !important;
    padding: 0.75rem 1.5rem !important;
}

/* Mobile responsive fixes */
@media (max-width: 991.98px) {
    .navbar-collapse {
        display: block !important;
        width: 100% !important;
    }
    
    .navbar-collapse.show {
        display: block !important;
    }
    
    .navbar-nav {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
        padding: 1rem 0 !important;
    }
    
    .navbar-nav .nav-item {
        display: block !important;
        width: 100% !important;
    }
    
    .navbar-nav .nav-link {
        display: flex !important;
        width: 100% !important;
        padding: 1rem 1.5rem !important;
        text-align: center !important;
        justify-content: center !important;
    }
}

/* Debug borders (remove in production) */
.debug-navbar * {
    border: 1px solid red !important;
}

.debug-navbar .navbar {
    border: 3px solid lime !important;
}

.debug-navbar .navbar-nav {
    border: 2px solid yellow !important;
}

.debug-navbar .nav-link {
    border: 1px solid cyan !important;
}

/* Force text to be visible */
.navbar-nav .nav-link,
.navbar-nav .nav-link span,
.navbar-nav .nav-link i {
    text-shadow: none !important;
    font-size: inherit !important;
    line-height: inherit !important;
}

/* Ensure no conflicting styles */
.navbar-nav .nav-link {
    background-image: none !important;
    background-color: transparent !important;
}

.navbar-nav .nav-link:hover {
    background-image: none !important;
}

/* Force button styles for mobile toggle */
.navbar-toggler {
    width: 40px !important;
    height: 40px !important;
    padding: 0.5rem !important;
    font-size: 1.25rem !important;
    line-height: 1 !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* Spacer for fixed navbar */
.navbar-spacer {
    height: 70px !important;
    display: block !important;
}
