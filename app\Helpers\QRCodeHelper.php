<?php

namespace App\Helpers;

use <PERSON>roid\QrCode\Builder\Builder;
use <PERSON>roid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\RoundBlockSizeMode;
use Endroid\QrCode\Writer\PngWriter;

class QRCodeHelper
{
    /**
     * Générer un QR code avec les paramètres par défaut de LocaSpace
     */
    public static function generate(string $data, int $size = 256, int $margin = 10): string
    {
        $result = Builder::create()
            ->writer(new PngWriter())
            ->data($data)
            ->encoding(new Encoding('UTF-8'))
            ->errorCorrectionLevel(ErrorCorrectionLevel::High)
            ->size($size)
            ->margin($margin)
            ->roundBlockSizeMode(RoundBlockSizeMode::Margin)
            ->build();

        return 'data:image/png;base64,' . base64_encode($result->getString());
    }

    /**
     * Générer un QR code d'authentification pour un utilisateur
     */
    public static function generateAuthQR(int $userId, string $token): string
    {
        $data = "LOCASPACE_AUTH:{$userId}:{$token}";
        return self::generate($data);
    }

    /**
     * Générer un QR code pour une réservation
     */
    public static function generateReservationQR(int $reservationId): string
    {
        $data = "LOCASPACE_RESERVATION:{$reservationId}";
        return self::generate($data);
    }

    /**
     * Générer un QR code pour un local
     */
    public static function generateLocalQR(int $localId): string
    {
        $data = "LOCASPACE_LOCAL:{$localId}";
        return self::generate($data);
    }

    /**
     * Générer un QR code pour une URL
     */
    public static function generateUrlQR(string $url): string
    {
        return self::generate($url);
    }

    /**
     * Valider le format d'un QR code LocaSpace
     */
    public static function validateQRData(string $qrData): array
    {
        // QR code d'authentification
        if (preg_match('/^LOCASPACE_AUTH:(\d+):(.+)$/', $qrData, $matches)) {
            return [
                'type' => 'auth',
                'user_id' => (int) $matches[1],
                'token' => $matches[2],
                'valid' => true
            ];
        }

        // QR code de réservation
        if (preg_match('/^LOCASPACE_RESERVATION:(\d+)$/', $qrData, $matches)) {
            return [
                'type' => 'reservation',
                'reservation_id' => (int) $matches[1],
                'valid' => true
            ];
        }

        // QR code de local
        if (preg_match('/^LOCASPACE_LOCAL:(\d+)$/', $qrData, $matches)) {
            return [
                'type' => 'local',
                'local_id' => (int) $matches[1],
                'valid' => true
            ];
        }

        // URL générique
        if (filter_var($qrData, FILTER_VALIDATE_URL)) {
            return [
                'type' => 'url',
                'url' => $qrData,
                'valid' => true
            ];
        }

        // Format non reconnu
        return [
            'type' => 'unknown',
            'data' => $qrData,
            'valid' => false
        ];
    }

    /**
     * Générer un token sécurisé pour l'authentification QR
     */
    public static function generateSecureToken(int $userId): string
    {
        return hash('sha256', $userId . time() . config('app.key'));
    }

    /**
     * Vérifier la validité d'un token QR
     */
    public static function validateToken(string $token, int $userId, int $maxAge = 3600): bool
    {
        // Dans un vrai système, vous stockeriez les tokens en base avec timestamp
        // Ici, c'est une validation basique
        return !empty($token) && strlen($token) === 64;
    }
}
