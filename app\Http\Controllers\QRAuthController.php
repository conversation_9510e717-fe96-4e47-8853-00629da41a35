<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class QRAuthController extends Controller
{
    /**
     * Afficher la page de scan QR pour connexion
     */
    public function showQRLogin()
    {
        return view('auth.qr-login');
    }

    /**
     * Générer un QR code pour l'utilisateur connecté
     */
    public function generateUserQR()
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Non authentifié'], 401);
        }

        $user = Auth::user();

        // Générer un token unique pour la session
        $token = Hash::make($user->id . time());

        // Stocker le token en session pour validation
        session(['qr_auth_token' => $token, 'qr_user_id' => $user->id]);

        // <PERSON>réer les données du QR code
        $qrData = 'LOCASPACE_AUTH:' . $user->id . ':' . $token;

        // Générer le QR code
        $qrCode = new QrCode($qrData);
        $qrCode->setSize(256);
        $qrCode->setMargin(10);

        $writer = new PngWriter();
        $result = $writer->write($qrCode);
        $qrCodeImage = $result->getString();

        return response()->json([
            'success' => true,
            'qr_data' => $qrData,
            'qr_image' => 'data:image/png;base64,' . base64_encode($qrCodeImage),
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email
            ]
        ]);
    }

    /**
     * Connexion via QR code scanné
     */
    public function loginWithQR(Request $request)
    {
        $request->validate([
            'qr_data' => 'required|string'
        ]);

        $qrData = $request->qr_data;

        // Vérifier le format du QR code
        if (!str_starts_with($qrData, 'LOCASPACE_AUTH:')) {
            return response()->json(['error' => 'QR code invalide'], 400);
        }

        // Extraire les données
        $parts = explode(':', str_replace('LOCASPACE_AUTH:', '', $qrData));

        if (count($parts) !== 2) {
            return response()->json(['error' => 'Format QR code invalide'], 400);
        }

        [$userId, $token] = $parts;

        // Vérifier que l'utilisateur existe
        $user = User::find($userId);
        if (!$user) {
            return response()->json(['error' => 'Utilisateur non trouvé'], 404);
        }

        // Vérifier le token (optionnel, pour plus de sécurité)
        // Dans un vrai système, vous stockeriez les tokens en base

        // Connecter l'utilisateur
        Auth::login($user);

        return response()->json([
            'success' => true,
            'message' => 'Connexion réussie',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email
            ],
            'redirect' => route('dashboard')
        ]);
    }

    /**
     * Générer un QR code pour une réservation
     */
    public function generateReservationQR($reservationId)
    {
        // Vérifier que la réservation existe et appartient à l'utilisateur
        $reservation = Auth::user()->reservations()->find($reservationId);

        if (!$reservation) {
            return response()->json(['error' => 'Réservation non trouvée'], 404);
        }

        // Créer les données du QR code
        $qrData = 'LOCASPACE_RESERVATION:' . $reservationId;

        // Générer le QR code
        $qrCode = new QrCode($qrData);
        $qrCode->setSize(256);
        $qrCode->setMargin(10);

        $writer = new PngWriter();
        $result = $writer->write($qrCode);
        $qrCodeImage = $result->getString();

        return response()->json([
            'success' => true,
            'qr_data' => $qrData,
            'qr_image' => 'data:image/png;base64,' . base64_encode($qrCodeImage),
            'reservation' => [
                'id' => $reservation->id,
                'local_name' => $reservation->local->name,
                'date' => $reservation->date,
                'time_start' => $reservation->time_start,
                'time_end' => $reservation->time_end
            ]
        ]);
    }
}
