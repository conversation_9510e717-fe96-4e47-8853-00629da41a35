/**
 * Enhanced Navbar JavaScript for LocaSpace
 * Fixes dropdown issues and improves user experience
 */

class EnhancedNavbar {
    constructor() {
        this.navbar = document.querySelector('.navbar');
        this.navbarCollapse = document.querySelector('.navbar-collapse');
        this.navbarToggler = document.querySelector('.navbar-toggler');
        this.dropdowns = document.querySelectorAll('.dropdown-toggle');
        this.isCollapsed = true;
        
        this.init();
    }
    
    init() {
        console.log('🚀 Enhanced Navbar initialized');
        this.setupEventListeners();
        this.fixDropdownIssues();
        this.setupScrollBehavior();
        this.setupMobileOptimizations();
        this.preventDropdownClose();
    }
    
    setupEventListeners() {
        // Navbar toggler
        if (this.navbarToggler) {
            this.navbarToggler.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleNavbar();
            });
        }
        
        // Dropdown toggles
        this.dropdowns.forEach(dropdown => {
            dropdown.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleDropdown(dropdown);
            });
            
            // Keyboard accessibility
            dropdown.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleDropdown(dropdown);
                }
            });
        });
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown')) {
                this.closeAllDropdowns();
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllDropdowns();
                if (window.innerWidth < 992 && !this.isCollapsed) {
                    this.collapseNavbar();
                }
            }
        });
    }
    
    toggleNavbar() {
        if (this.isCollapsed) {
            this.expandNavbar();
        } else {
            this.collapseNavbar();
        }
    }
    
    expandNavbar() {
        this.navbarCollapse.classList.add('show');
        this.navbarToggler.setAttribute('aria-expanded', 'true');
        this.isCollapsed = false;
        
        // Add animation
        this.navbarCollapse.style.animation = 'slideDown 0.3s ease-out';
        
        console.log('📱 Navbar expanded');
    }
    
    collapseNavbar() {
        this.navbarCollapse.classList.remove('show');
        this.navbarToggler.setAttribute('aria-expanded', 'false');
        this.isCollapsed = true;
        
        // Close all dropdowns when collapsing
        this.closeAllDropdowns();
        
        console.log('📱 Navbar collapsed');
    }
    
    toggleDropdown(dropdownToggle) {
        const dropdownMenu = dropdownToggle.nextElementSibling;
        const isOpen = dropdownMenu.classList.contains('show');
        
        // Close all other dropdowns first
        this.closeAllDropdowns();
        
        if (!isOpen) {
            this.openDropdown(dropdownToggle, dropdownMenu);
        }
    }
    
    openDropdown(dropdownToggle, dropdownMenu) {
        dropdownMenu.classList.add('show');
        dropdownToggle.setAttribute('aria-expanded', 'true');
        
        // Add animation
        dropdownMenu.style.animation = 'dropdownFadeIn 0.3s ease-out';
        
        // Position dropdown correctly on mobile
        if (window.innerWidth < 992) {
            dropdownMenu.style.position = 'static';
            dropdownMenu.style.transform = 'none';
            dropdownMenu.style.marginTop = '0.5rem';
        }
        
        console.log('🔽 Dropdown opened:', dropdownToggle.textContent.trim());
    }
    
    closeAllDropdowns() {
        this.dropdowns.forEach(dropdown => {
            const dropdownMenu = dropdown.nextElementSibling;
            if (dropdownMenu && dropdownMenu.classList.contains('show')) {
                dropdownMenu.classList.remove('show');
                dropdown.setAttribute('aria-expanded', 'false');
            }
        });
    }
    
    fixDropdownIssues() {
        // Fix Bootstrap dropdown auto-close issue
        this.dropdowns.forEach(dropdown => {
            const dropdownMenu = dropdown.nextElementSibling;
            if (dropdownMenu) {
                // Prevent dropdown from closing when clicking inside
                dropdownMenu.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
                
                // Only close on actual link clicks
                const dropdownItems = dropdownMenu.querySelectorAll('.dropdown-item');
                dropdownItems.forEach(item => {
                    item.addEventListener('click', (e) => {
                        // Allow navigation but close dropdown
                        setTimeout(() => {
                            this.closeAllDropdowns();
                        }, 100);
                    });
                });
            }
        });
    }
    
    preventDropdownClose() {
        // Prevent Bootstrap from auto-managing dropdowns
        this.dropdowns.forEach(dropdown => {
            dropdown.removeAttribute('data-bs-toggle');
            dropdown.removeAttribute('data-bs-auto-close');
        });
    }
    
    setupScrollBehavior() {
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // Scrolling down - hide navbar
                this.navbar.style.transform = 'translateY(-100%)';
            } else {
                // Scrolling up - show navbar
                this.navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        });
    }
    
    setupMobileOptimizations() {
        // Close navbar when clicking on nav links (mobile)
        const navLinks = document.querySelectorAll('.nav-link:not(.dropdown-toggle)');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth < 992 && !this.isCollapsed) {
                    setTimeout(() => {
                        this.collapseNavbar();
                    }, 300);
                }
            });
        });
    }
    
    handleResize() {
        const width = window.innerWidth;
        
        if (width >= 992) {
            // Desktop mode
            this.navbarCollapse.classList.remove('show');
            this.navbarToggler.setAttribute('aria-expanded', 'false');
            this.isCollapsed = true;
            
            // Reset dropdown positioning
            this.dropdowns.forEach(dropdown => {
                const dropdownMenu = dropdown.nextElementSibling;
                if (dropdownMenu) {
                    dropdownMenu.style.position = '';
                    dropdownMenu.style.transform = '';
                    dropdownMenu.style.marginTop = '';
                }
            });
        }
        
        console.log('📏 Window resized:', width + 'px');
    }
    
    // Public methods for testing
    testDropdowns() {
        console.log('🧪 Testing dropdowns...');
        const results = [];
        
        this.dropdowns.forEach((dropdown, index) => {
            const menu = dropdown.nextElementSibling;
            const hasMenu = menu && menu.classList.contains('dropdown-menu');
            const text = dropdown.textContent.trim();
            
            results.push({
                index: index + 1,
                text,
                hasMenu,
                isWorking: hasMenu
            });
        });
        
        console.table(results);
        return results;
    }
    
    testResponsive() {
        console.log('📱 Testing responsive behavior...');
        const width = window.innerWidth;
        const isMobile = width < 992;
        
        const results = {
            screenWidth: width,
            isMobile,
            togglerVisible: this.navbarToggler ? window.getComputedStyle(this.navbarToggler).display !== 'none' : false,
            navbarCollapsed: this.isCollapsed,
            dropdownCount: this.dropdowns.length
        };
        
        console.table(results);
        return results;
    }
}

// CSS Animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from {
            opacity: 0;
            max-height: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            max-height: 500px;
            transform: translateY(0);
        }
    }
    
    @keyframes dropdownFadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .navbar {
        transition: transform 0.3s ease-in-out;
    }
    
    .dropdown-menu {
        transition: all 0.3s ease-out;
    }
    
    .navbar-collapse {
        transition: all 0.3s ease-out;
    }
`;
document.head.appendChild(style);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.enhancedNavbar = new EnhancedNavbar();
    
    // Global test functions
    window.testNavbarDropdowns = () => window.enhancedNavbar.testDropdowns();
    window.testNavbarResponsive = () => window.enhancedNavbar.testResponsive();
    window.toggleNavbarTest = () => window.enhancedNavbar.toggleNavbar();
    
    console.log('🎯 Enhanced Navbar Commands:');
    console.log('- testNavbarDropdowns() : Test dropdown functionality');
    console.log('- testNavbarResponsive() : Test responsive behavior');
    console.log('- toggleNavbarTest() : Toggle navbar manually');
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedNavbar;
}
