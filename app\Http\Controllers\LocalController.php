<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Local;
use Illuminate\Support\Facades\Validator;

class LocalController extends Controller
{
    /**
     * Display a listing of locals.
     */
    public function index(Request $request)
    {
        $query = Local::active();

        // Filter by type
        if ($request->filled('type')) {
            $query->ofType($request->type);
        }

        // Filter by location
        if ($request->filled('location')) {
            $query->where('location', 'like', '%' . $request->location . '%');
        }

        // Filter by capacity
        if ($request->filled('capacity')) {
            $query->where('capacity', '>=', $request->capacity);
        }

        // Filter by equipment
        if ($request->filled('equipment')) {
            $equipment = $request->equipment;
            $query->whereJsonContains('equipment', $equipment);
        }

        $locals = $query->paginate(12);

        return view('locals.index', compact('locals'));
    }

    /**
     * Show the form for creating a new local.
     */
    public function create()
    {
        return view('locals.create');
    }

    /**
     * Store a newly created local in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|in:sport,conference,fête',
            'location' => 'required|string|max:255',
            'capacity' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'equipment' => 'array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        Local::create([
            'name' => $request->name,
            'type' => $request->type,
            'location' => $request->location,
            'capacity' => $request->capacity,
            'price' => $request->price,
            'equipment' => $request->equipment ?? [],
            'status' => true,
        ]);

        return redirect()->route('locals.index')->with('success', 'Local créé avec succès.');
    }

    /**
     * Display the specified local.
     */
    public function show(Local $local)
    {
        return view('locals.show', compact('local'));
    }

    /**
     * Show the form for editing the specified local.
     */
    public function edit(Local $local)
    {
        return view('locals.edit', compact('local'));
    }

    /**
     * Update the specified local in storage.
     */
    public function update(Request $request, Local $local)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|in:sport,conference,fête',
            'location' => 'required|string|max:255',
            'capacity' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'equipment' => 'array',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $local->update([
            'name' => $request->name,
            'type' => $request->type,
            'location' => $request->location,
            'capacity' => $request->capacity,
            'price' => $request->price,
            'equipment' => $request->equipment ?? [],
            'status' => $request->boolean('status'),
        ]);

        return redirect()->route('locals.index')->with('success', 'Local mis à jour avec succès.');
    }

    /**
     * Remove the specified local from storage.
     */
    public function destroy(Local $local)
    {
        $local->delete();
        return redirect()->route('locals.index')->with('success', 'Local supprimé avec succès.');
    }

    /**
     * Get availability for a specific local.
     */
    public function availability(Request $request, Local $local)
    {
        $date = $request->get('date', now()->format('Y-m-d'));

        $reservations = $local->reservations()
            ->onDate($date)
            ->where('status', '!=', 'annulée')
            ->get(['start_time', 'end_time', 'status']);

        return response()->json([
            'date' => $date,
            'reservations' => $reservations,
        ]);
    }
}
