@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-3">
                <i class="fas fa-building me-2"></i>Nos locaux disponibles
            </h1>
            
            <!-- Filters -->
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('locals.index') }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="type" class="form-label">Type de local</label>
                                <select name="type" id="type" class="form-select">
                                    <option value="">Tous les types</option>
                                    <option value="sport" {{ request('type') === 'sport' ? 'selected' : '' }}>Terrains de sport</option>
                                    <option value="conference" {{ request('type') === 'conference' ? 'selected' : '' }}>Salles de conférences</option>
                                    <option value="fête" {{ request('type') === 'fête' ? 'selected' : '' }}>Salles de fêtes</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="location" class="form-label">Localisation</label>
                                <input type="text" name="location" id="location" class="form-control" 
                                       placeholder="Ville, quartier..." value="{{ request('location') }}">
                            </div>
                            <div class="col-md-2">
                                <label for="capacity" class="form-label">Capacité min.</label>
                                <input type="number" name="capacity" id="capacity" class="form-control" 
                                       placeholder="Ex: 10" value="{{ request('capacity') }}">
                            </div>
                            <div class="col-md-2">
                                <label for="equipment" class="form-label">Équipement</label>
                                <select name="equipment" id="equipment" class="form-select">
                                    <option value="">Tous</option>
                                    <option value="wifi" {{ request('equipment') === 'wifi' ? 'selected' : '' }}>WiFi</option>
                                    <option value="projecteur" {{ request('equipment') === 'projecteur' ? 'selected' : '' }}>Projecteur</option>
                                    <option value="climatisation" {{ request('equipment') === 'climatisation' ? 'selected' : '' }}>Climatisation</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>Filtrer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results -->
    <div class="row">
        @if($locals->count() > 0)
            @foreach($locals as $local)
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            @if($local->type === 'sport')
                                <i class="fas fa-futbol text-success me-2"></i>
                            @elseif($local->type === 'conference')
                                <i class="fas fa-presentation-screen text-primary me-2"></i>
                            @else
                                <i class="fas fa-glass-cheers text-warning me-2"></i>
                            @endif
                            <span class="badge bg-secondary">{{ ucfirst($local->type) }}</span>
                        </div>
                        <div class="text-end">
                            <span class="h5 text-success mb-0">{{ $local->price }}€</span>
                            <small class="text-muted">/heure</small>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">{{ $local->name }}</h5>
                        <p class="card-text">
                            <i class="fas fa-map-marker-alt text-muted me-1"></i>
                            {{ $local->location }}
                        </p>
                        <p class="card-text">
                            <i class="fas fa-users text-muted me-1"></i>
                            Capacité : {{ $local->capacity }} personnes
                        </p>
                        
                        @if($local->equipment && count($local->equipment) > 0)
                        <div class="mb-3">
                            <small class="text-muted">Équipements :</small><br>
                            @foreach($local->equipment as $equipment)
                                <span class="badge bg-light text-dark me-1">
                                    @if($equipment === 'wifi')
                                        <i class="fas fa-wifi me-1"></i>WiFi
                                    @elseif($equipment === 'projecteur')
                                        <i class="fas fa-video me-1"></i>Projecteur
                                    @elseif($equipment === 'climatisation')
                                        <i class="fas fa-snowflake me-1"></i>Climatisation
                                    @else
                                        {{ $equipment }}
                                    @endif
                                </span>
                            @endforeach
                        </div>
                        @endif
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>Détails
                            </a>
                            @auth
                                <a href="{{ route('reservations.create', $local) }}" class="btn btn-primary">
                                    <i class="fas fa-calendar-plus me-1"></i>Réserver
                                </a>
                            @else
                                <a href="{{ route('login') }}" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-1"></i>Se connecter
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        @else
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>Aucun local trouvé</h4>
                    <p class="text-muted">Essayez de modifier vos critères de recherche.</p>
                    <a href="{{ route('locals.index') }}" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>Réinitialiser les filtres
                    </a>
                </div>
            </div>
        @endif
    </div>

    <!-- Pagination -->
    @if($locals->hasPages())
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                {{ $locals->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
