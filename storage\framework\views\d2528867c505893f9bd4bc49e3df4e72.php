<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'type',
    'size' => 'md'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'type',
    'size' => 'md'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $typeConfig = [
        'sport' => [
            'icon' => 'fas fa-futbol',
            'color' => 'text-success',
            'label' => 'Sport'
        ],
        'conference' => [
            'icon' => 'fas fa-presentation-screen',
            'color' => 'text-primary',
            'label' => 'Conférence'
        ],
        'fête' => [
            'icon' => 'fas fa-glass-cheers',
            'color' => 'text-warning',
            'label' => 'Fête'
        ]
    ];
    
    $sizeClasses = [
        'sm' => '',
        'md' => 'fa-lg',
        'lg' => 'fa-2x',
        'xl' => 'fa-3x'
    ];
    
    $config = $typeConfig[$type] ?? [
        'icon' => 'fas fa-building',
        'color' => 'text-muted',
        'label' => ucfirst($type)
    ];
    
    $sizeClass = $sizeClasses[$size] ?? 'fa-lg';
?>

<i class="<?php echo e($config['icon']); ?> <?php echo e($config['color']); ?> <?php echo e($sizeClass); ?>" 
   title="<?php echo e($config['label']); ?>" 
   <?php echo e($attributes); ?>></i>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\hackathon\locaspace\resources\views/components/local-icon.blade.php ENDPATH**/ ?>