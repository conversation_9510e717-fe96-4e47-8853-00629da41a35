@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-user me-2"></i>Mon profil
            </h1>
            <p class="text-muted">Gérez vos informations personnelles et votre QR code</p>
        </div>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Informations personnelles
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('profile.update') }}">
                        @csrf
                        @method('PUT')

                        <!-- Name -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>Nom complet
                                </label>
                                <input type="text" 
                                       class="form-control @error('name') is-invalid @enderror" 
                                       id="name" 
                                       name="name" 
                                       value="{{ old('name', $user->name) }}" 
                                       required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Adresse email
                                </label>
                                <input type="email" 
                                       class="form-control @error('email') is-invalid @enderror" 
                                       id="email" 
                                       name="email" 
                                       value="{{ old('email', $user->email) }}" 
                                       required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Role Display -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-shield-alt me-1"></i>Rôle
                                </label>
                                <div class="form-control-plaintext">
                                    @if($user->isAdmin())
                                        <span class="badge bg-danger">Administrateur</span>
                                    @else
                                        <span class="badge bg-primary">Client</span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Membre depuis
                                </label>
                                <div class="form-control-plaintext">
                                    {{ $user->created_at->format('d/m/Y') }}
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Password Change -->
                        <h6 class="mb-3">
                            <i class="fas fa-lock me-2"></i>Changer le mot de passe
                        </h6>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="current_password" class="form-label">Mot de passe actuel</label>
                                <input type="password" 
                                       class="form-control @error('current_password') is-invalid @enderror" 
                                       id="current_password" 
                                       name="current_password">
                                @error('current_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="password" class="form-label">Nouveau mot de passe</label>
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password">
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="password_confirmation" class="form-label">Confirmer le mot de passe</label>
                                <input type="password" 
                                       class="form-control" 
                                       id="password_confirmation" 
                                       name="password_confirmation">
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Mettre à jour le profil
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Account Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistiques du compte
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-primary">{{ $user->reservations()->count() }}</h4>
                                <small class="text-muted">Réservations totales</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">{{ $user->reservations()->confirmed()->count() }}</h4>
                                <small class="text-muted">Confirmées</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-warning">{{ $user->reservations()->pending()->count() }}</h4>
                                <small class="text-muted">En attente</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-danger">{{ $user->reservations()->cancelled()->count() }}</h4>
                            <small class="text-muted">Annulées</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- QR Code Section -->
        <div class="col-lg-4">
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-qrcode me-2"></i>Mon QR Code
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <!-- QR Code Placeholder -->
                        <div class="bg-light border rounded p-4 mb-3" style="min-height: 200px;">
                            <i class="fas fa-qrcode fa-5x text-muted mb-3"></i>
                            <p class="text-muted">QR Code généré automatiquement</p>
                            <small class="text-muted">{{ $user->qr_code }}</small>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary" onclick="downloadQR()">
                            <i class="fas fa-download me-2"></i>Télécharger
                        </button>
                        <form method="POST" action="{{ route('profile.regenerate-qr') }}" style="display: inline;">
                            @csrf
                            <button type="submit" class="btn btn-outline-warning w-100" 
                                    onclick="return confirm('Êtes-vous sûr de vouloir régénérer votre QR code ?')">
                                <i class="fas fa-sync me-2"></i>Régénérer
                            </button>
                        </form>
                    </div>

                    <div class="alert alert-info mt-3" role="alert">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            Utilisez ce QR code pour vous connecter rapidement à LocaSpace
                        </small>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Actions rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('reservations.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>Mes réservations
                        </a>
                        <a href="{{ route('locals.index') }}" class="btn btn-outline-success">
                            <i class="fas fa-search me-2"></i>Réserver un local
                        </a>
                        <a href="{{ route('dashboard') }}" class="btn btn-outline-info">
                            <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function downloadQR() {
    // Placeholder for QR code download functionality
    alert('Fonctionnalité de téléchargement QR à implémenter');
}
</script>
@endpush
@endsection
