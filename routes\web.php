<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\LocalController;
use App\Http\Controllers\ReservationController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\AdminController;

// Page d'accueil
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Page d'information réseau
Route::get('/network-info', function () {
    return view('network-info');
})->name('network.info');

// Page de test caméra
Route::get('/camera-test', function () {
    return view('camera-test');
})->name('camera.test');

// Page de test QR (debug)
Route::get('/qr-test', function () {
    return view('qr-test');
})->name('qr.test');

// Page de test navbar (à supprimer en production)
Route::get('/test-navbar', function () {
    return view('test-navbar');
})->name('test.navbar');

// Page de test navbar avec utilisateur connecté (à supprimer en production)
Route::get('/test-navbar-auth', function () {
    // Simuler un utilisateur connecté pour les tests
    if (config('app.debug')) {
        $user = \App\Models\User::first();
        if ($user) {
            \Illuminate\Support\Facades\Auth::login($user);
        }
    }
    return view('test-navbar');
})->name('test.navbar.auth');

// Guide de test (à supprimer en production)
Route::get('/test-guide', function () {
    return view('test-guide');
})->name('test.guide');

// Routes d'authentification
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
    Route::get('/qr-login', [AuthController::class, 'showQRLoginForm'])->name('qr.login');
    Route::post('/qr-login', [AuthController::class, 'qrLogin']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Routes pour les locaux (publiques)
Route::get('/locals', [LocalController::class, 'index'])->name('locals.index');
Route::get('/locals/{local}', [LocalController::class, 'show'])->name('locals.show');
Route::get('/locals/{local}/availability', [LocalController::class, 'availability'])->name('locals.availability');

// Routes protégées par authentification
Route::middleware('auth')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Profil utilisateur
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/regenerate-qr', [ProfileController::class, 'regenerateQR'])->name('profile.regenerate-qr');

    // Réservations
    Route::get('/reservations', [ReservationController::class, 'index'])->name('reservations.index');
    Route::get('/reservations/create/{local}', [ReservationController::class, 'create'])->name('reservations.create');
    Route::post('/reservations', [ReservationController::class, 'store'])->name('reservations.store');
    Route::get('/reservations/{reservation}', [ReservationController::class, 'show'])->name('reservations.show');
    Route::patch('/reservations/{reservation}/cancel', [ReservationController::class, 'cancel'])->name('reservations.cancel');
    Route::get('/locals/{local}/calendar', [ReservationController::class, 'calendar'])->name('locals.calendar');

    // Factures
    Route::get('/invoices/{invoice}', [InvoiceController::class, 'show'])->name('invoices.show');
    Route::get('/invoices/{invoice}/download', [InvoiceController::class, 'download'])->name('invoices.download');
    Route::post('/invoices/{invoice}/pay', [InvoiceController::class, 'pay'])->name('invoices.pay');
});

// Routes administrateur
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

    // Gestion des locaux
    Route::get('/locals', [LocalController::class, 'index'])->name('locals.index');
    Route::get('/locals/create', [LocalController::class, 'create'])->name('locals.create');
    Route::post('/locals', [LocalController::class, 'store'])->name('locals.store');
    Route::get('/locals/{local}/edit', [LocalController::class, 'edit'])->name('locals.edit');
    Route::put('/locals/{local}', [LocalController::class, 'update'])->name('locals.update');
    Route::delete('/locals/{local}', [LocalController::class, 'destroy'])->name('locals.destroy');

    // Gestion des réservations
    Route::get('/reservations', [AdminController::class, 'reservations'])->name('reservations.index');
    Route::patch('/reservations/{reservation}/confirm', [ReservationController::class, 'confirm'])->name('reservations.confirm');

    // Gestion des utilisateurs
    Route::get('/users', [AdminController::class, 'users'])->name('users.index');
    Route::get('/users/{user}', [AdminController::class, 'showUser'])->name('users.show');

    // Rapports et statistiques
    Route::get('/reports', [AdminController::class, 'reports'])->name('reports');
    Route::get('/invoices', [AdminController::class, 'invoices'])->name('invoices.index');
});

// API Routes for QR and Stripe
Route::prefix('api')->middleware('web')->group(function () {
    // QR Code API
    Route::post('/qr/generate-user', [App\Http\Controllers\QRAuthController::class, 'generateUserQR'])->middleware('auth');
    Route::post('/qr/login', [App\Http\Controllers\QRAuthController::class, 'loginWithQR']);
    Route::get('/qr/reservation/{id}', [App\Http\Controllers\QRAuthController::class, 'generateReservationQR'])->middleware('auth');

    // Test API QR (debug)
    Route::get('/qr/test', function() {
        return response()->json([
            'success' => true,
            'message' => 'API QR fonctionnelle',
            'user' => auth()->check() ? auth()->user()->name : 'Non connecté',
            'timestamp' => now()
        ]);
    });

    // Stripe API
    Route::post('/stripe/create-payment-intent', [App\Http\Controllers\StripeController::class, 'createPaymentIntent'])->middleware('auth');
    Route::post('/stripe/confirm-payment', [App\Http\Controllers\StripeController::class, 'confirmPayment'])->middleware('auth');
});

// Stripe Webhook (sans middleware CSRF)
Route::post('/stripe/webhook', [App\Http\Controllers\StripeController::class, 'webhook'])->withoutMiddleware(['web']);
