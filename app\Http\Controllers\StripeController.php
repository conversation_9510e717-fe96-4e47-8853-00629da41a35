<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Customer;
use App\Models\Reservation;

class StripeController extends Controller
{
    public function __construct()
    {
        // Configurer Stripe avec la clé secrète
        Stripe::setApiKey(env('STRIPE_SECRET_KEY'));
    }

    /**
     * Créer un Payment Intent pour une réservation
     */
    public function createPaymentIntent(Request $request)
    {
        $request->validate([
            'reservation_id' => 'required|exists:reservations,id',
            'amount' => 'required|numeric|min:1'
        ]);

        try {
            $reservation = Reservation::findOrFail($request->reservation_id);
            $user = Auth::user();

            // Vérifier que la réservation appartient à l'utilisateur
            if ($reservation->user_id !== $user->id) {
                return response()->json(['error' => 'Réservation non autorisée'], 403);
            }

            // Créer ou récupérer le customer Stripe
            $customer = $this->getOrCreateStripeCustomer($user);

            // Créer le Payment Intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $request->amount * 100, // Montant en centimes
                'currency' => 'eur',
                'customer' => $customer->id,
                'metadata' => [
                    'reservation_id' => $reservation->id,
                    'user_id' => $user->id,
                    'local_name' => $reservation->local->name
                ],
                'description' => "Réservation {$reservation->local->name} - {$reservation->date}"
            ]);

            return response()->json([
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
                'amount' => $request->amount,
                'currency' => 'EUR'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la création du paiement: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Confirmer le paiement
     */
    public function confirmPayment(Request $request)
    {
        $request->validate([
            'payment_intent_id' => 'required|string',
            'reservation_id' => 'required|exists:reservations,id'
        ]);

        try {
            // Récupérer le Payment Intent
            $paymentIntent = PaymentIntent::retrieve($request->payment_intent_id);

            if ($paymentIntent->status === 'succeeded') {
                // Mettre à jour la réservation
                $reservation = Reservation::findOrFail($request->reservation_id);
                $reservation->update([
                    'payment_status' => 'paid',
                    'payment_intent_id' => $paymentIntent->id,
                    'amount_paid' => $paymentIntent->amount / 100
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Paiement confirmé avec succès',
                    'reservation' => $reservation
                ]);
            } else {
                return response()->json([
                    'error' => 'Le paiement n\'a pas été confirmé'
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la confirmation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Webhook Stripe pour les événements de paiement
     */
    public function webhook(Request $request)
    {
        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');
        $endpoint_secret = env('STRIPE_WEBHOOK_SECRET');

        try {
            $event = \Stripe\Webhook::constructEvent(
                $payload, $sig_header, $endpoint_secret
            );
        } catch(\UnexpectedValueException $e) {
            return response('Invalid payload', 400);
        } catch(\Stripe\Exception\SignatureVerificationException $e) {
            return response('Invalid signature', 400);
        }

        // Traiter l'événement
        switch ($event->type) {
            case 'payment_intent.succeeded':
                $paymentIntent = $event->data->object;
                $this->handleSuccessfulPayment($paymentIntent);
                break;

            case 'payment_intent.payment_failed':
                $paymentIntent = $event->data->object;
                $this->handleFailedPayment($paymentIntent);
                break;

            default:
                // Événement non géré
                break;
        }

        return response('Success', 200);
    }

    /**
     * Obtenir ou créer un customer Stripe
     */
    private function getOrCreateStripeCustomer($user)
    {
        if ($user->stripe_customer_id) {
            try {
                return Customer::retrieve($user->stripe_customer_id);
            } catch (\Exception $e) {
                // Customer n'existe plus, en créer un nouveau
            }
        }

        // Créer un nouveau customer
        $customer = Customer::create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id
            ]
        ]);

        // Sauvegarder l'ID customer
        $user->update(['stripe_customer_id' => $customer->id]);

        return $customer;
    }

    /**
     * Gérer un paiement réussi
     */
    private function handleSuccessfulPayment($paymentIntent)
    {
        $reservationId = $paymentIntent->metadata->reservation_id ?? null;

        if ($reservationId) {
            $reservation = Reservation::find($reservationId);
            if ($reservation) {
                $reservation->update([
                    'payment_status' => 'paid',
                    'payment_intent_id' => $paymentIntent->id,
                    'amount_paid' => $paymentIntent->amount / 100
                ]);
            }
        }
    }

    /**
     * Gérer un paiement échoué
     */
    private function handleFailedPayment($paymentIntent)
    {
        $reservationId = $paymentIntent->metadata->reservation_id ?? null;

        if ($reservationId) {
            $reservation = Reservation::find($reservationId);
            if ($reservation) {
                $reservation->update([
                    'payment_status' => 'failed'
                ]);
            }
        }
    }
}
