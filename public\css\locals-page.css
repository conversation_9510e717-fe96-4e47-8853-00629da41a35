/* ===== LOCALS PAGE STYLES ===== */

/* Hero Section */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.z-index-1 {
    z-index: 1;
}

/* Filter Card */
.filter-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

/* Local Cards */
.local-item {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.local-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: all 0.6s ease;
    z-index: 1;
}

.local-item:hover::before {
    left: 100%;
}

.local-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.local-item:hover .card-img-top {
    transform: scale(1.05);
}

/* Card Image */
.card-img-top {
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.card-img-top::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.1), transparent);
}

/* Price Tag */
.price-tag {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.local-item:hover .price-tag {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Equipment Badges */
.badge {
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.1);
}

/* Rating Stars */
.rating-stars {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* View Toggle Buttons */
.view-toggle {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.view-toggle .btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.view-toggle .btn.active {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    transform: scale(1.05);
}

/* List View Specific */
.list-view .local-card {
    animation: slideInLeft 0.6s ease-out;
}

.list-view .card {
    border-radius: 16px;
    overflow: hidden;
}

.list-view .card-body {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Stats Section */
.stat-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
}

.stat-item h3 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* No Results Section */
.no-results {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(233, 236, 239, 0.9));
    backdrop-filter: blur(10px);
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.no-results::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 123, 255, 0.05) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

/* Pagination */
.pagination-wrapper .pagination {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.pagination .page-link {
    border: none;
    border-radius: 8px;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
    background: transparent;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

/* Loading States */
.loading-card {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Hover Effects */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Filter Form Enhancements */
.form-select:focus,
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    transform: scale(1.02);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
}

/* Button Enhancements */
.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #007bff, #0056b3);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .local-card {
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0 !important;
    }
    
    .hero-section h1 {
        font-size: 2rem !important;
    }
    
    .local-item:hover {
        transform: translateY(-5px) scale(1.01);
    }
    
    .view-toggle {
        margin-bottom: 1rem;
    }
    
    .stat-item {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 2rem 0 !important;
    }
    
    .hero-section h1 {
        font-size: 1.5rem !important;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .price-tag {
        font-size: 0.875rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .local-item {
        background: rgba(33, 37, 41, 0.95);
        color: #f8f9fa;
    }
    
    .filter-card {
        background: rgba(33, 37, 41, 0.95);
        color: #f8f9fa;
    }
    
    .stat-item {
        background: linear-gradient(135deg, rgba(33, 37, 41, 0.9), rgba(52, 58, 64, 0.9));
        color: #f8f9fa;
    }
}

/* Print Styles */
@media print {
    .hero-section,
    .view-toggle,
    .filter-card {
        display: none !important;
    }
    
    .local-item {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
