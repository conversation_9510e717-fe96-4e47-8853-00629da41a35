/* ===== QR CODE SCANNER SYSTEM ===== */

class QRCodeScanner {
    constructor() {
        this.scanner = null;
        this.isScanning = false;
        this.videoElement = null;
        this.canvasElement = null;
        this.resultElement = null;
        this.stream = null;
        
        this.init();
    }
    
    init() {
        console.log('🔍 QR Scanner initialized');
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Bouton pour démarrer le scan
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-qr-scan]')) {
                e.preventDefault();
                this.startScanning();
            }
            
            if (e.target.matches('[data-qr-stop]')) {
                e.preventDefault();
                this.stopScanning();
            }
            
            if (e.target.matches('[data-qr-generate]')) {
                e.preventDefault();
                this.generateQRCode(e.target.dataset.qrGenerate);
            }
        });
    }
    
    async startScanning() {
        try {
            console.log('📷 Starting QR scan...');
            
            // Créer l'interface de scan
            this.createScanInterface();
            
            // Demander l'accès à la caméra
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: { 
                    facingMode: 'environment', // Caméra arrière
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            });
            
            this.videoElement.srcObject = this.stream;
            this.videoElement.play();
            
            this.isScanning = true;
            this.scanFrame();
            
            // Afficher l'interface
            document.getElementById('qr-scanner-modal').style.display = 'block';
            
        } catch (error) {
            console.error('❌ Error starting camera:', error);
            this.showError('Impossible d\'accéder à la caméra. Vérifiez les permissions.');
        }
    }
    
    createScanInterface() {
        // Supprimer l'interface existante si elle existe
        const existingModal = document.getElementById('qr-scanner-modal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // Créer l'interface de scan
        const modalHTML = `
            <div id="qr-scanner-modal" class="qr-scanner-modal">
                <div class="qr-scanner-content">
                    <div class="qr-scanner-header">
                        <h3><i class="fas fa-qrcode me-2"></i>Scanner QR Code</h3>
                        <button class="qr-close-btn" data-qr-stop>
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="qr-scanner-body">
                        <div class="camera-container">
                            <video id="qr-video" autoplay muted playsinline></video>
                            <canvas id="qr-canvas" style="display: none;"></canvas>
                            
                            <!-- Overlay de scan -->
                            <div class="scan-overlay">
                                <div class="scan-frame">
                                    <div class="scan-corners">
                                        <div class="corner top-left"></div>
                                        <div class="corner top-right"></div>
                                        <div class="corner bottom-left"></div>
                                        <div class="corner bottom-right"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Instructions -->
                            <div class="scan-instructions">
                                <p><i class="fas fa-camera me-2"></i>Placez le QR code dans le cadre</p>
                                <div class="scan-status" id="scan-status">
                                    <i class="fas fa-search me-2"></i>Recherche en cours...
                                </div>
                            </div>
                        </div>
                        
                        <!-- Résultat -->
                        <div id="qr-result" class="qr-result" style="display: none;">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <span id="qr-result-text"></span>
                            </div>
                        </div>
                        
                        <!-- Erreur -->
                        <div id="qr-error" class="qr-error" style="display: none;">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span id="qr-error-text"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="qr-scanner-footer">
                        <button class="btn btn-secondary" data-qr-stop>
                            <i class="fas fa-times me-2"></i>Annuler
                        </button>
                        <button class="btn btn-primary" onclick="qrScanner.switchCamera()">
                            <i class="fas fa-sync-alt me-2"></i>Changer caméra
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        this.videoElement = document.getElementById('qr-video');
        this.canvasElement = document.getElementById('qr-canvas');
        this.resultElement = document.getElementById('qr-result');
    }
    
    scanFrame() {
        if (!this.isScanning) return;
        
        try {
            if (this.videoElement.readyState === this.videoElement.HAVE_ENOUGH_DATA) {
                // Configurer le canvas
                this.canvasElement.width = this.videoElement.videoWidth;
                this.canvasElement.height = this.videoElement.videoHeight;
                
                const context = this.canvasElement.getContext('2d');
                context.drawImage(this.videoElement, 0, 0);
                
                // Obtenir les données d'image
                const imageData = context.getImageData(0, 0, this.canvasElement.width, this.canvasElement.height);
                
                // Scanner avec jsQR
                const code = jsQR(imageData.data, imageData.width, imageData.height);
                
                if (code) {
                    console.log('✅ QR Code detected:', code.data);
                    this.handleQRResult(code.data);
                    return;
                }
            }
        } catch (error) {
            console.error('❌ Scan error:', error);
        }
        
        // Continuer le scan
        requestAnimationFrame(() => this.scanFrame());
    }
    
    handleQRResult(data) {
        console.log('🎯 QR Result:', data);
        
        // Arrêter le scan
        this.isScanning = false;
        
        // Afficher le résultat
        document.getElementById('qr-result-text').textContent = data;
        document.getElementById('qr-result').style.display = 'block';
        document.getElementById('scan-status').style.display = 'none';
        
        // Traiter selon le type de QR code
        if (data.startsWith('LOCASPACE_AUTH:')) {
            this.handleAuthQR(data);
        } else if (data.startsWith('LOCASPACE_RESERVATION:')) {
            this.handleReservationQR(data);
        } else if (data.startsWith('http')) {
            this.handleURLQR(data);
        } else {
            this.handleGenericQR(data);
        }
        
        // Fermer automatiquement après 3 secondes
        setTimeout(() => {
            this.stopScanning();
        }, 3000);
    }
    
    handleAuthQR(data) {
        // Extraire l'ID utilisateur du QR code
        const userId = data.replace('LOCASPACE_AUTH:', '');
        
        // Rediriger vers la page de connexion avec l'ID
        window.location.href = `/auth/qr-login/${userId}`;
    }
    
    handleReservationQR(data) {
        // Extraire l'ID de réservation
        const reservationId = data.replace('LOCASPACE_RESERVATION:', '');
        
        // Rediriger vers la réservation
        window.location.href = `/reservations/${reservationId}`;
    }
    
    handleURLQR(data) {
        // Ouvrir l'URL
        if (confirm('Ouvrir ce lien ?\n' + data)) {
            window.open(data, '_blank');
        }
    }
    
    handleGenericQR(data) {
        // Afficher le contenu générique
        alert('QR Code scanné :\n' + data);
    }
    
    stopScanning() {
        console.log('🛑 Stopping QR scan...');
        
        this.isScanning = false;
        
        // Arrêter le stream vidéo
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        
        // Fermer l'interface
        const modal = document.getElementById('qr-scanner-modal');
        if (modal) {
            modal.remove();
        }
    }
    
    async switchCamera() {
        try {
            // Arrêter le stream actuel
            if (this.stream) {
                this.stream.getTracks().forEach(track => track.stop());
            }
            
            // Basculer entre caméra avant et arrière
            const currentFacing = this.stream?.getVideoTracks()[0]?.getSettings()?.facingMode || 'environment';
            const newFacing = currentFacing === 'environment' ? 'user' : 'environment';
            
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: { 
                    facingMode: newFacing,
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            });
            
            this.videoElement.srcObject = this.stream;
            this.videoElement.play();
            
        } catch (error) {
            console.error('❌ Error switching camera:', error);
            this.showError('Impossible de changer de caméra');
        }
    }
    
    generateQRCode(data) {
        console.log('🎨 Generating QR code for:', data);
        
        // Créer l'interface de génération
        this.createGenerateInterface(data);
    }
    
    createGenerateInterface(data) {
        const modalHTML = `
            <div id="qr-generate-modal" class="qr-scanner-modal">
                <div class="qr-scanner-content">
                    <div class="qr-scanner-header">
                        <h3><i class="fas fa-qrcode me-2"></i>QR Code généré</h3>
                        <button class="qr-close-btn" onclick="document.getElementById('qr-generate-modal').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="qr-scanner-body text-center">
                        <div id="qr-code-container" class="qr-code-container">
                            <!-- QR code sera généré ici -->
                        </div>
                        
                        <div class="qr-info mt-3">
                            <p class="text-muted">Scannez ce code avec votre téléphone</p>
                            <small class="text-muted">${data}</small>
                        </div>
                    </div>
                    
                    <div class="qr-scanner-footer">
                        <button class="btn btn-secondary" onclick="document.getElementById('qr-generate-modal').remove()">
                            <i class="fas fa-times me-2"></i>Fermer
                        </button>
                        <button class="btn btn-primary" onclick="qrScanner.downloadQR()">
                            <i class="fas fa-download me-2"></i>Télécharger
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Générer le QR code avec QRCode.js
        new QRCode(document.getElementById('qr-code-container'), {
            text: data,
            width: 256,
            height: 256,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
        });
        
        document.getElementById('qr-generate-modal').style.display = 'block';
    }
    
    downloadQR() {
        const canvas = document.querySelector('#qr-code-container canvas');
        if (canvas) {
            const link = document.createElement('a');
            link.download = 'qrcode.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    }
    
    showError(message) {
        document.getElementById('qr-error-text').textContent = message;
        document.getElementById('qr-error').style.display = 'block';
        document.getElementById('scan-status').style.display = 'none';
    }
}

// Initialiser le scanner
let qrScanner;
document.addEventListener('DOMContentLoaded', function() {
    qrScanner = new QRCodeScanner();
    console.log('✅ QR Scanner ready');
});

// Fonctions globales
window.startQRScan = () => qrScanner.startScanning();
window.stopQRScan = () => qrScanner.stopScanning();
window.generateQR = (data) => qrScanner.generateQRCode(data);
