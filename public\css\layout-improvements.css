/* ===== LAYOUT IMPROVEMENTS - BETTER CONTENT WIDTH ===== */

/* Container improvements */
.container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

/* Responsive container widths */
@media (min-width: 576px) {
    .container {
        max-width: 540px !important;
        padding-left: 1.5rem !important;
        padding-right: 1.5rem !important;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px !important;
        padding-left: 2rem !important;
        padding-right: 2rem !important;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px !important;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px !important;
    }
}

@media (min-width: 1400px) {
    .container {
        max-width: 1200px !important;
    }
}

/* Content sections with better spacing */
.content-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.content-section-wide {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.content-section-narrow {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Hero sections with controlled width */
.hero-section {
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 3rem 2rem !important;
}

/* Card grids with better spacing */
.card-grid {
    display: grid;
    gap: 2rem;
    margin: 2rem 0;
}

.card-grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.card-grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.card-grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Better form layouts */
.form-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
}

.form-container-wide {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

/* Table responsive improvements */
.table-container {
    max-width: 100%;
    overflow-x: auto;
    margin: 1rem 0;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Content with sidebar layout */
.content-with-sidebar {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

@media (max-width: 992px) {
    .content-with-sidebar {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Dashboard layout */
.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

/* Article/blog layout */
.article-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
    line-height: 1.8;
}

/* Centered content blocks */
.centered-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    padding: 3rem 1rem;
}

.centered-content-wide {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
    padding: 3rem 1rem;
}

/* Section spacing improvements */
.section {
    padding: 4rem 0;
}

.section-sm {
    padding: 2rem 0;
}

.section-lg {
    padding: 6rem 0;
}

/* Responsive spacing */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
    
    .hero-section {
        padding: 2rem 1rem !important;
    }
    
    .content-section,
    .content-section-wide,
    .content-section-narrow {
        padding: 1rem;
    }
    
    .section {
        padding: 2rem 0;
    }
    
    .section-lg {
        padding: 3rem 0;
    }
    
    .card-grid {
        gap: 1rem;
    }
    
    .card-grid-2,
    .card-grid-3,
    .card-grid-4 {
        grid-template-columns: 1fr;
    }
}

/* Utility classes for width control */
.w-auto { width: auto !important; }
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }

.mw-100 { max-width: 100% !important; }
.mw-75 { max-width: 75% !important; }
.mw-50 { max-width: 50% !important; }

/* Margin and padding utilities */
.mx-auto { margin-left: auto !important; margin-right: auto !important; }
.px-responsive { padding-left: 1rem; padding-right: 1rem; }

@media (min-width: 768px) {
    .px-responsive { padding-left: 2rem; padding-right: 2rem; }
}

@media (min-width: 1200px) {
    .px-responsive { padding-left: 3rem; padding-right: 3rem; }
}

/* Content overflow fixes */
.content-wrapper {
    overflow-x: hidden;
    width: 100%;
}

/* Prevent horizontal scroll */
body {
    overflow-x: hidden !important;
}

.row {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.row > * {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
}

/* Better breakpoints for cards */
@media (min-width: 576px) {
    .col-sm-6 { flex: 0 0 auto; width: 50%; }
}

@media (min-width: 768px) {
    .col-md-4 { flex: 0 0 auto; width: 33.333333%; }
    .col-md-6 { flex: 0 0 auto; width: 50%; }
}

@media (min-width: 992px) {
    .col-lg-3 { flex: 0 0 auto; width: 25%; }
    .col-lg-4 { flex: 0 0 auto; width: 33.333333%; }
    .col-lg-6 { flex: 0 0 auto; width: 50%; }
}

@media (min-width: 1200px) {
    .col-xl-2 { flex: 0 0 auto; width: 16.666667%; }
    .col-xl-3 { flex: 0 0 auto; width: 25%; }
    .col-xl-4 { flex: 0 0 auto; width: 33.333333%; }
}

/* Improved card layouts */
.card-deck {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.card-deck-sm {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.card-deck-lg {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

/* Navigation improvements */
.navbar .container {
    max-width: 1200px !important;
}

/* Footer improvements */
.footer .container {
    max-width: 1200px !important;
}

/* Print styles */
@media print {
    .container {
        max-width: none !important;
        padding: 0 !important;
    }
    
    .hero-section {
        padding: 1rem !important;
    }
}
