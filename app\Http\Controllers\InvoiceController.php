<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Invoice;
use Illuminate\Support\Facades\Auth;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class InvoiceController extends Controller
{
    /**
     * Display the specified invoice.
     */
    public function show(Invoice $invoice)
    {
        // Check if user owns this invoice or is admin
        if ($invoice->reservation->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403);
        }

        $invoice->load(['reservation.user', 'reservation.local']);
        return view('invoices.show', compact('invoice'));
    }

    /**
     * Download the invoice PDF.
     */
    public function download(Invoice $invoice)
    {
        // Check if user owns this invoice or is admin
        if ($invoice->reservation->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403);
        }

        $invoice->load(['reservation.user', 'reservation.local']);

        // Generate PDF if not exists
        if (!$invoice->pdf_path || !Storage::exists($invoice->pdf_path)) {
            $this->generatePDF($invoice);
        }

        return Storage::download($invoice->pdf_path, 'facture-' . $invoice->id . '.pdf');
    }

    /**
     * Process payment for the invoice.
     */
    public function pay(Request $request, Invoice $invoice)
    {
        // Check if user owns this invoice
        if ($invoice->reservation->user_id !== Auth::id()) {
            abort(403);
        }

        // Check if invoice is already paid
        if ($invoice->isPaid()) {
            return back()->withErrors([
                'error' => 'Cette facture a déjà été payée.',
            ]);
        }

        // Here you would integrate with Stripe or another payment processor
        // For now, we'll just mark it as paid (simulation)

        try {
            // Simulate payment processing
            $invoice->markAsPaid();

            // Update reservation status to confirmed
            $invoice->reservation->update(['status' => 'confirmée']);

            // Generate PDF invoice
            $this->generatePDF($invoice);

            return redirect()->route('invoices.show', $invoice)
                ->with('success', 'Paiement effectué avec succès. Votre réservation est confirmée.');

        } catch (\Exception $e) {
            return back()->withErrors([
                'error' => 'Une erreur est survenue lors du paiement.',
            ]);
        }
    }

    /**
     * Generate PDF for the invoice.
     */
    private function generatePDF(Invoice $invoice)
    {
        $invoice->load(['reservation.user', 'reservation.local']);

        $pdf = Pdf::loadView('invoices.pdf', compact('invoice'));

        $filename = 'invoices/invoice-' . $invoice->id . '.pdf';
        Storage::put($filename, $pdf->output());

        $invoice->update(['pdf_path' => $filename]);

        return $filename;
    }
}
