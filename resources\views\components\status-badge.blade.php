@props([
    'status',
    'type' => 'reservation' // reservation, payment, local
])

@php
    $statusConfig = [
        'reservation' => [
            'confirmée' => [
                'class' => 'bg-success',
                'icon' => 'fas fa-check',
                'text' => 'Confirmée'
            ],
            'en attente' => [
                'class' => 'bg-warning',
                'icon' => 'fas fa-clock',
                'text' => 'En attente'
            ],
            'annulée' => [
                'class' => 'bg-danger',
                'icon' => 'fas fa-times',
                'text' => 'Annulée'
            ]
        ],
        'payment' => [
            'réglé' => [
                'class' => 'bg-success',
                'icon' => 'fas fa-check-circle',
                'text' => 'Payé'
            ],
            'non réglé' => [
                'class' => 'bg-danger',
                'icon' => 'fas fa-exclamation-circle',
                'text' => 'Non payé'
            ]
        ],
        'local' => [
            'actif' => [
                'class' => 'bg-success',
                'icon' => 'fas fa-check',
                'text' => 'Actif'
            ],
            'inactif' => [
                'class' => 'bg-secondary',
                'icon' => 'fas fa-pause',
                'text' => 'Inactif'
            ]
        ]
    ];
    
    $config = $statusConfig[$type][$status] ?? [
        'class' => 'bg-secondary',
        'icon' => 'fas fa-question',
        'text' => ucfirst($status)
    ];
@endphp

<span class="badge {{ $config['class'] }}">
    <i class="{{ $config['icon'] }} me-1"></i>{{ $config['text'] }}
</span>
