<!-- Navigation Bar Component -->
<nav class="navbar navbar-expand-lg navbar-dark shadow-lg fixed-top" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <!-- Brand -->
        <a class="navbar-brand fw-bold fs-3 d-flex align-items-center" href="<?php echo e(route('home')); ?>" style="transition: transform 0.3s ease;">
            <div class="brand-icon me-2" style="background: rgba(255,255,255,0.2); padding: 8px; border-radius: 8px;">
                <i class="fas fa-building text-white"></i>
            </div>
            <span class="brand-text">LocaSpace</span>
        </a>

        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent"
                aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navbar Content -->
        <div class="collapse navbar-collapse" id="navbarContent">
            <!-- Left Navigation -->
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('home') ? 'active' : ''); ?>" href="<?php echo e(route('home')); ?>">
                        <i class="fas fa-home me-1"></i>
                        <span>Accueil</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('locals.*') ? 'active' : ''); ?>" href="<?php echo e(route('locals.index')); ?>">
                        <i class="fas fa-search me-1"></i>
                        <span>Locaux</span>
                    </a>
                </li>
               



                <?php if(auth()->guard()->check()): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard')); ?>">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('reservations.*') ? 'active' : ''); ?>" href="<?php echo e(route('reservations.index')); ?>">
                            <i class="fas fa-calendar-alt me-1"></i>
                            <span>Réservations</span>
                        </a>
                    </li>

                    <?php if(Auth::user()->isAdmin()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('admin.*') ? 'active' : ''); ?>"
                               href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-cogs me-1"></i>
                                <span>Admin</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-modern" aria-labelledby="adminDropdown">
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-chart-bar me-2"></i>Tableau de bord
                                    </h6>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo e(route('admin.dashboard')); ?>">
                                        <i class="fas fa-chart-line me-2"></i>Dashboard Admin
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-cogs me-2"></i>Gestion
                                    </h6>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo e(route('admin.locals.index')); ?>">
                                        <i class="fas fa-building me-2"></i>Locaux
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo e(route('admin.reservations.index')); ?>">
                                        <i class="fas fa-calendar-check me-2"></i>Réservations
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo e(route('admin.users.index')); ?>">
                                        <i class="fas fa-users me-2"></i>Utilisateurs
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo e(route('admin.reports')); ?>">
                                        <i class="fas fa-chart-pie me-2"></i>Rapports
                                    </a>
                                </li>
                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
            </ul>

            <!-- Right Navigation -->
            <ul class="navbar-nav ms-auto">
                <?php if(auth()->guard()->guest()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('qr.login')); ?>">
                            <i class="fas fa-qrcode me-1"></i>
                            <span>QR Login</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('login')); ?>">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            <span>Connexion</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light btn-sm ms-2 px-3" href="<?php echo e(route('register')); ?>">
                            <i class="fas fa-user-plus me-1"></i>
                            <span>Inscription</span>
                        </a>
                    </li>
                <?php else: ?>
                    <!-- Notifications -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link position-relative" href="#" id="notificationDropdown"
                           role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell fs-5"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-pulse">
                                3
                                <span class="visually-hidden">notifications non lues</span>
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end dropdown-menu-modern notification-dropdown"
                            aria-labelledby="notificationDropdown">
                            <li>
                                <h6 class="dropdown-header">
                                    <i class="fas fa-bell me-2"></i>Notifications
                                </h6>
                            </li>
                            <li>
                                <a class="dropdown-item notification-item" href="#">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-check-circle text-success fa-lg"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="notification-title">Réservation confirmée</h6>
                                            <p class="notification-text">Votre réservation du 15/12 a été confirmée</p>
                                            <small class="text-muted">Il y a 2 heures</small>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-center" href="#">
                                    <small>Voir toutes les notifications</small>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle user-dropdown" href="#" id="userDropdown"
                           role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="user-info d-none d-md-block">
                                    <span class="user-name"><?php echo e(Auth::user()->name); ?></span>
                                    <?php if(Auth::user()->isAdmin()): ?>
                                        <span class="user-badge">Admin</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end dropdown-menu-modern user-menu"
                            aria-labelledby="userDropdown">
                            <li>
                                <div class="dropdown-header user-header">
                                    <div class="user-avatar-large">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="user-details">
                                        <h6 class="mb-0"><?php echo e(Auth::user()->name); ?></h6>
                                        <small class="text-muted"><?php echo e(Auth::user()->email); ?></small>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('profile.show')); ?>">
                                    <i class="fas fa-user me-2"></i>Mon profil
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('reservations.index')); ?>">
                                    <i class="fas fa-calendar-alt me-2"></i>Mes réservations
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

<!-- Spacer for fixed navbar -->
<div class="navbar-spacer"></div>

<style>
/* Navbar Brand Hover Effect */
.navbar-brand:hover {
    transform: scale(1.05) !important;
}

/* Nav Links Hover Effects */
.nav-link {
    position: relative;
    transition: all 0.3s ease !important;
    border-radius: 8px !important;
    margin: 0 4px !important;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-2px) !important;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.25) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Dropdown Improvements */
.dropdown-menu {
    border: none !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
    border-radius: 12px !important;
    backdrop-filter: blur(10px) !important;
    background: rgba(255, 255, 255, 0.95) !important;
}

.dropdown-item {
    transition: all 0.3s ease !important;
    border-radius: 8px !important;
    margin: 2px 8px !important;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    transform: translateX(5px) !important;
}

/* User Avatar */
.user-avatar {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.user-dropdown:hover .user-avatar {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Notification Badge Animation */
.notification-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
    100% { transform: translate(-50%, -50%) scale(1); }
}

/* Mobile Improvements */
@media (max-width: 991.98px) {
    .nav-link:hover {
        transform: none !important;
        background: rgba(255, 255, 255, 0.1) !important;
    }

    .navbar-brand:hover {
        transform: none !important;
    }
}

/* Glassmorphism Effect */
.navbar {
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}
</style>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\hackathon\locaspace\resources\views/components/navbar.blade.php ENDPATH**/ ?>