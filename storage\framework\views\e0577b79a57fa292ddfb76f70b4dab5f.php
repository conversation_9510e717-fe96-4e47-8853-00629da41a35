<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-bug me-2"></i>Test QR Code - Debug</h4>
                </div>
                <div class="card-body">
                    <!-- État de connexion -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user me-2"></i>État de connexion :</h6>
                        <?php if(auth()->guard()->check()): ?>
                            <p class="mb-0">✅ <strong>Connecté</strong> en tant que : <?php echo e(auth()->user()->name); ?> (<?php echo e(auth()->user()->email); ?>)</p>
                        <?php else: ?>
                            <p class="mb-0">❌ <strong>Non connecté</strong> - <a href="<?php echo e(route('login')); ?>">Se connecter</a></p>
                        <?php endif; ?>
                    </div>

                    <!-- Token CSRF -->
                    <div class="alert alert-secondary">
                        <h6><i class="fas fa-shield-alt me-2"></i>Token CSRF :</h6>
                        <code><?php echo e(csrf_token()); ?></code>
                    </div>

                    <!-- Tests API -->
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6><i class="fas fa-flask me-2"></i>Test API Simple</h6>
                                    <button class="btn btn-primary" onclick="testSimpleAPI()">
                                        <i class="fas fa-play me-2"></i>Test GET /api/qr/test
                                    </button>
                                </div>
                            </div>
                        </div>

                        <?php if(auth()->guard()->check()): ?>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6><i class="fas fa-qrcode me-2"></i>Test Génération QR</h6>
                                    <button class="btn btn-success" onclick="testQRGeneration()">
                                        <i class="fas fa-play me-2"></i>Test POST /api/qr/generate-user
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Résultats -->
                    <div class="mt-4">
                        <h6><i class="fas fa-terminal me-2"></i>Résultats :</h6>
                        <div id="test-results" class="bg-dark text-light p-3 rounded" style="min-height: 200px; font-family: monospace; font-size: 0.9rem;">
                            <div class="text-muted">Cliquez sur un bouton de test pour voir les résultats...</div>
                        </div>
                    </div>

                    <!-- QR Code généré -->
                    <div id="qr-result" class="mt-4" style="display: none;">
                        <h6><i class="fas fa-qrcode me-2"></i>QR Code généré :</h6>
                        <div class="text-center">
                            <div id="qr-image-container"></div>
                            <button class="btn btn-outline-primary mt-2" onclick="downloadQR()" id="download-btn" style="display: none;">
                                <i class="fas fa-download me-2"></i>Télécharger
                            </button>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="mt-4 text-center">
                        <button class="btn btn-warning" onclick="clearResults()">
                            <i class="fas fa-trash me-2"></i>Effacer les résultats
                        </button>
                        <a href="<?php echo e(route('profile')); ?>" class="btn btn-info">
                            <i class="fas fa-user me-2"></i>Aller au profil
                        </a>
                        <a href="<?php echo e(route('qr.login')); ?>" class="btn btn-secondary">
                            <i class="fas fa-qrcode me-2"></i>Scanner QR
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let currentQRImage = null;

function log(message, type = 'info') {
    const results = document.getElementById('test-results');
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
        info: '#17a2b8',
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107'
    };
    
    const logEntry = document.createElement('div');
    logEntry.innerHTML = `<span style="color: ${colors[type]}">[${timestamp}]</span> ${message}`;
    results.appendChild(logEntry);
    results.scrollTop = results.scrollHeight;
}

function clearResults() {
    document.getElementById('test-results').innerHTML = '<div class="text-muted">Résultats effacés...</div>';
    document.getElementById('qr-result').style.display = 'none';
    currentQRImage = null;
}

async function testSimpleAPI() {
    log('🧪 Test de l\'API simple...', 'info');
    
    try {
        const response = await fetch('/api/qr/test');
        log(`📡 Status: ${response.status}`, response.ok ? 'success' : 'error');
        
        const data = await response.json();
        log(`📦 Réponse: ${JSON.stringify(data, null, 2)}`, 'success');
        
    } catch (error) {
        log(`❌ Erreur: ${error.message}`, 'error');
    }
}

async function testQRGeneration() {
    log('🔄 Test de génération QR...', 'info');
    
    try {
        // Vérifier le token CSRF
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            log('❌ Token CSRF manquant', 'error');
            return;
        }
        
        log(`🔐 Token CSRF: ${csrfToken.getAttribute('content').substring(0, 20)}...`, 'info');
        
        const response = await fetch('/api/qr/generate-user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                'Accept': 'application/json'
            }
        });
        
        log(`📡 Status: ${response.status}`, response.ok ? 'success' : 'error');
        log(`📡 Headers: ${JSON.stringify([...response.headers.entries()])}`, 'info');
        
        const text = await response.text();
        log(`📦 Raw response: ${text.substring(0, 200)}${text.length > 200 ? '...' : ''}`, 'info');
        
        try {
            const data = JSON.parse(text);
            log(`📦 Parsed data: ${JSON.stringify(data, null, 2)}`, 'success');
            
            if (data.success && data.qr_image) {
                displayQRCode(data.qr_image);
                log('✅ QR Code affiché avec succès', 'success');
            }
            
        } catch (parseError) {
            log(`❌ Erreur de parsing JSON: ${parseError.message}`, 'error');
        }
        
    } catch (error) {
        log(`❌ Erreur réseau: ${error.message}`, 'error');
    }
}

function displayQRCode(qrImage) {
    const container = document.getElementById('qr-image-container');
    const resultDiv = document.getElementById('qr-result');
    const downloadBtn = document.getElementById('download-btn');
    
    container.innerHTML = `<img src="${qrImage}" alt="QR Code" class="img-fluid" style="max-width: 256px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">`;
    resultDiv.style.display = 'block';
    downloadBtn.style.display = 'inline-block';
    
    currentQRImage = qrImage;
}

function downloadQR() {
    if (currentQRImage) {
        const link = document.createElement('a');
        link.download = 'qr-code-test.png';
        link.href = currentQRImage;
        link.click();
        log('📥 QR Code téléchargé', 'success');
    } else {
        log('❌ Aucun QR Code à télécharger', 'error');
    }
}

// Auto-test au chargement
document.addEventListener('DOMContentLoaded', function() {
    log('🚀 Page de test QR chargée', 'info');
    log(`🌐 URL actuelle: ${window.location.href}`, 'info');
    log(`🔐 CSRF Token présent: ${document.querySelector('meta[name="csrf-token"]') ? 'Oui' : 'Non'}`, 'info');
    
    <?php if(auth()->guard()->check()): ?>
        log('👤 Utilisateur connecté: <?php echo e(auth()->user()->name); ?>', 'success');
    <?php else: ?>
        log('👤 Utilisateur non connecté', 'warning');
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\hackathon\locaspace\resources\views/qr-test.blade.php ENDPATH**/ ?>