<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un utilisateur admin
        User::create([
            'name' => 'Administrateur LocaSpace',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'qr_code' => 'QR_ADMIN_' . md5('<EMAIL>' . time()),
            'role' => 'admin',
        ]);

        // Créer quelques utilisateurs clients
        User::create([
            'name' => 'Mohammed Alami',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'qr_code' => 'QR_USER_' . md5('<EMAIL>' . time()),
            'role' => 'client',
        ]);

        User::create([
            'name' => 'Fatima Benali',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'qr_code' => 'QR_USER_' . md5('<EMAIL>' . time()),
            'role' => 'client',
        ]);

        User::create([
            'name' => 'Youssef Tazi',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'qr_code' => 'QR_USER_' . md5('<EMAIL>' . time()),
            'role' => 'client',
        ]);

        // Créer des utilisateurs supplémentaires avec Factory si disponible
        if (class_exists(\Database\Factories\UserFactory::class)) {
            User::factory(10)->create();
        }
    }
}
