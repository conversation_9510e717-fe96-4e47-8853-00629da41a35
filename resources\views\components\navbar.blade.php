<!-- Navigation Bar Component -->
<nav class="navbar navbar-expand-lg navbar-dark bg-gradient-primary shadow-lg fixed-top">
    <div class="container">
        <!-- Brand -->
        <a class="navbar-brand fw-bold fs-3" href="{{ route('home') }}">
            <x-logo size="md" />
        </a>

        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent"
                aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navbar Content -->
        <div class="collapse navbar-collapse" id="navbarContent">
            <!-- Left Navigation -->
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">
                        <i class="fas fa-home me-1"></i>
                        <span>Accueil</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('locals.*') ? 'active' : '' }}" href="{{ route('locals.index') }}">
                        <i class="fas fa-search me-1"></i>
                        <span>Locaux</span>
                    </a>
                </li>

                @auth
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('reservations.*') ? 'active' : '' }}" href="{{ route('reservations.index') }}">
                            <i class="fas fa-calendar-alt me-1"></i>
                            <span>Réservations</span>
                        </a>
                    </li>

                    @if(Auth::user()->isAdmin())
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.*') ? 'active' : '' }}"
                               href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-cogs me-1"></i>
                                <span>Admin</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-modern" aria-labelledby="adminDropdown">
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-chart-bar me-2"></i>Tableau de bord
                                    </h6>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.dashboard') }}">
                                        <i class="fas fa-chart-line me-2"></i>Dashboard Admin
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-cogs me-2"></i>Gestion
                                    </h6>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.locals.index') }}">
                                        <i class="fas fa-building me-2"></i>Locaux
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.reservations.index') }}">
                                        <i class="fas fa-calendar-check me-2"></i>Réservations
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.users.index') }}">
                                        <i class="fas fa-users me-2"></i>Utilisateurs
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.reports') }}">
                                        <i class="fas fa-chart-pie me-2"></i>Rapports
                                    </a>
                                </li>
                            </ul>
                        </li>
                    @endif
                @endauth
            </ul>

            <!-- Right Navigation -->
            <ul class="navbar-nav ms-auto">
                @guest
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('qr.login') }}">
                            <i class="fas fa-qrcode me-1"></i>
                            <span>QR Login</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            <span>Connexion</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light btn-sm ms-2 px-3" href="{{ route('register') }}">
                            <i class="fas fa-user-plus me-1"></i>
                            <span>Inscription</span>
                        </a>
                    </li>
                @else
                    <!-- Notifications -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link position-relative" href="#" id="notificationDropdown"
                           role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell fs-5"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-pulse">
                                3
                                <span class="visually-hidden">notifications non lues</span>
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end dropdown-menu-modern notification-dropdown"
                            aria-labelledby="notificationDropdown">
                            <li>
                                <h6 class="dropdown-header">
                                    <i class="fas fa-bell me-2"></i>Notifications
                                </h6>
                            </li>
                            <li>
                                <a class="dropdown-item notification-item" href="#">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-check-circle text-success fa-lg"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="notification-title">Réservation confirmée</h6>
                                            <p class="notification-text">Votre réservation du 15/12 a été confirmée</p>
                                            <small class="text-muted">Il y a 2 heures</small>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-center" href="#">
                                    <small>Voir toutes les notifications</small>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle user-dropdown" href="#" id="userDropdown"
                           role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="user-info d-none d-md-block">
                                    <span class="user-name">{{ Auth::user()->name }}</span>
                                    @if(Auth::user()->isAdmin())
                                        <span class="user-badge">Admin</span>
                                    @endif
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end dropdown-menu-modern user-menu"
                            aria-labelledby="userDropdown">
                            <li>
                                <div class="dropdown-header user-header">
                                    <div class="user-avatar-large">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="user-details">
                                        <h6 class="mb-0">{{ Auth::user()->name }}</h6>
                                        <small class="text-muted">{{ Auth::user()->email }}</small>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ route('profile.show') }}">
                                    <i class="fas fa-user me-2"></i>Mon profil
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('reservations.index') }}">
                                    <i class="fas fa-calendar-alt me-2"></i>Mes réservations
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                @endguest
            </ul>
        </div>
    </div>
</nav>

<!-- Spacer for fixed navbar -->
<div class="navbar-spacer"></div>
