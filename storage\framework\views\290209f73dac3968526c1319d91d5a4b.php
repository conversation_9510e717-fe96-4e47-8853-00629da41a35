<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning text-dark text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-qrcode me-2"></i>Connexion QR Code
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Instructions -->
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Comment ça marche :</strong>
                        <ol class="mb-0 mt-2">
                            <li>Saisissez votre code QR personnel</li>
                            <li>Ou scannez-le avec votre appareil photo</li>
                            <li>Connectez-vous instantanément !</li>
                        </ol>
                    </div>

                    <form method="POST" action="<?php echo e(route('qr.login')); ?>">
                        <?php echo csrf_field(); ?>

                        <!-- QR Code Input -->
                        <div class="mb-4">
                            <label for="qr_code" class="form-label">
                                <i class="fas fa-qrcode me-1"></i>Code QR
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg <?php $__errorArgs = ['qr_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="qr_code" 
                                   name="qr_code" 
                                   placeholder="Saisissez ou scannez votre QR code" 
                                   required 
                                   autofocus>
                            <?php $__errorArgs = ['qr_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback">
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- QR Scanner Button -->
                        <div class="mb-4 text-center">
                            <button type="button" class="btn btn-outline-primary" id="scanQR">
                                <i class="fas fa-camera me-2"></i>Scanner avec la caméra
                            </button>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Se connecter avec QR
                            </button>
                        </div>
                    </form>

                    <!-- Divider -->
                    <hr class="my-4">

                    <!-- Alternative Login Methods -->
                    <div class="text-center">
                        <p class="text-muted mb-3">Ou utilisez la méthode classique :</p>
                        <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-envelope me-2"></i>Email / Mot de passe
                        </a>
                    </div>

                    <!-- Help Section -->
                    <div class="mt-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-question-circle me-2"></i>Où trouver mon QR code ?
                                </h6>
                                <p class="card-text small mb-0">
                                    Votre QR code personnel est disponible dans votre profil après inscription. 
                                    Vous pouvez l'imprimer ou le sauvegarder sur votre téléphone pour un accès rapide.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Links -->
                    <div class="text-center mt-4">
                        <p class="mb-0">
                            Pas encore de compte ? 
                            <a href="<?php echo e(route('register')); ?>" class="text-decoration-none">
                                S'inscrire gratuitement
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.getElementById('scanQR').addEventListener('click', function() {
    // Placeholder for QR scanner functionality
    // In a real implementation, you would integrate with a QR scanner library
    alert('Fonctionnalité de scan QR à implémenter avec une bibliothèque comme QuaggaJS ou ZXing');
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\hackathon\locaspace\resources\views/auth/qr-login.blade.php ENDPATH**/ ?>