@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">Test de la Navbar</h1>

            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>Instructions de test</h5>
                <p>Cette page permet de tester la navbar sur différentes tailles d'écran :</p>
                <ul>
                    <li><strong>Desktop (PC) :</strong> Tous les éléments doivent être visibles horizontalement</li>
                    <li><strong>Mobile :</strong> Le bouton hamburger doit apparaître et révéler le menu au clic</li>
                    <li><strong>Dropdowns :</strong> Les menus déroulants doivent fonctionner correctement</li>
                </ul>

                <div class="mt-3 p-3 bg-light rounded">
                    <h6><i class="fas fa-user me-2"></i>État actuel :</h6>
                    <ul class="mb-0">
                        @auth
                            <li class="text-success">✅ Utilisateur connecté : <strong>{{ Auth::user()->name }}</strong></li>
                            @if(Auth::user()->isAdmin())
                                <li class="text-warning">👑 Rôle : Administrateur</li>
                            @else
                                <li class="text-info">👤 Rôle : Client</li>
                            @endif
                        @else
                            <li class="text-warning">⚠️ Utilisateur non connecté (mode invité)</li>
                            <li class="text-muted">💡 <a href="{{ route('test.navbar.auth') }}">Tester avec utilisateur connecté</a></li>
                        @endauth
                        <li class="text-info">🖥️ Largeur écran : <span id="screen-width">{{ request()->header('User-Agent') ? 'Détection JS...' : 'Inconnue' }}</span></li>
                    </ul>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-desktop me-2"></i>Vue Desktop</h5>
                        </div>
                        <div class="card-body">
                            <p>Sur un écran large (≥992px), vous devriez voir :</p>
                            <ul>
                                <li>Logo LocaSpace à gauche</li>
                                <li>Menu principal horizontal</li>
                                <li>Menu utilisateur à droite</li>
                                <li>Pas de bouton hamburger</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-mobile-alt me-2"></i>Vue Mobile</h5>
                        </div>
                        <div class="card-body">
                            <p>Sur un écran petit (&lt;992px), vous devriez voir :</p>
                            <ul>
                                <li>Logo LocaSpace à gauche</li>
                                <li>Bouton hamburger à droite</li>
                                <li>Menu collapsible au clic</li>
                                <li>Éléments centrés verticalement</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Éléments de navigation disponibles</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Pour tous les utilisateurs :</h6>
                                    <ul>
                                        <li>Accueil</li>
                                        <li>Locaux</li>
                                        @guest
                                            <li>QR Login</li>
                                            <li>Connexion</li>
                                            <li>Inscription</li>
                                        @endguest
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    @auth
                                        <h6>Pour les utilisateurs connectés :</h6>
                                        <ul>
                                            <li>Dashboard</li>
                                            <li>Réservations</li>
                                            @if(Auth::user()->isAdmin())
                                                <li>Menu Admin (dropdown)</li>
                                            @endif
                                            <li>Menu Utilisateur (dropdown)</li>
                                        </ul>
                                    @endauth
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tools me-2"></i>Actions de test</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-primary" onclick="testResponsive()">
                                    <i class="fas fa-mobile-alt me-2"></i>Simuler mobile
                                </button>
                                <button class="btn btn-success" onclick="testDropdowns()">
                                    <i class="fas fa-caret-down me-2"></i>Tester dropdowns
                                </button>
                                <button class="btn btn-info" onclick="showNavbarInfo()">
                                    <i class="fas fa-info me-2"></i>Infos navbar
                                </button>
                                <button class="btn btn-warning" onclick="toggleNavbar()">
                                    <i class="fas fa-eye me-2"></i>Toggle navbar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div id="test-results" class="card d-none">
                        <div class="card-header">
                            <h5><i class="fas fa-check-circle me-2"></i>Résultats des tests</h5>
                        </div>
                        <div class="card-body">
                            <div id="test-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function testResponsive() {
    const navbar = document.querySelector('.navbar');
    const toggler = document.querySelector('.navbar-toggler');
    const collapse = document.querySelector('.navbar-collapse');

    let results = '<h6>Test Responsive :</h6><ul>';

    // Test si le toggler existe
    if (toggler) {
        results += '<li class="text-success"><i class="fas fa-check me-2"></i>Bouton hamburger présent</li>';
    } else {
        results += '<li class="text-danger"><i class="fas fa-times me-2"></i>Bouton hamburger manquant</li>';
    }

    // Test si le collapse existe
    if (collapse) {
        results += '<li class="text-success"><i class="fas fa-check me-2"></i>Menu collapsible présent</li>';
    } else {
        results += '<li class="text-danger"><i class="fas fa-times me-2"></i>Menu collapsible manquant</li>';
    }

    // Test des classes Bootstrap
    if (navbar && navbar.classList.contains('navbar-expand-lg')) {
        results += '<li class="text-success"><i class="fas fa-check me-2"></i>Classes responsive correctes</li>';
    } else {
        results += '<li class="text-warning"><i class="fas fa-exclamation me-2"></i>Classes responsive à vérifier</li>';
    }

    results += '</ul>';
    showResults(results);
}

function testDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown-toggle');

    let results = '<h6>Test Dropdowns :</h6><ul>';
    results += `<li class="text-info"><i class="fas fa-info me-2"></i>Nombre de dropdowns trouvés : ${dropdowns.length}</li>`;

    dropdowns.forEach((dropdown, index) => {
        const menu = dropdown.nextElementSibling;
        if (menu && menu.classList.contains('dropdown-menu')) {
            results += `<li class="text-success"><i class="fas fa-check me-2"></i>Dropdown ${index + 1} : Menu présent</li>`;
        } else {
            results += `<li class="text-danger"><i class="fas fa-times me-2"></i>Dropdown ${index + 1} : Menu manquant</li>`;
        }
    });

    results += '</ul>';
    showResults(results);
}

function showNavbarInfo() {
    const navbar = document.querySelector('.navbar');
    const width = window.innerWidth;

    let results = '<h6>Informations Navbar :</h6><ul>';
    results += `<li><strong>Largeur écran :</strong> ${width}px</li>`;
    results += `<li><strong>Type d\'écran :</strong> ${width >= 992 ? 'Desktop' : 'Mobile'}</li>`;

    if (navbar) {
        results += `<li><strong>Classes navbar :</strong> ${navbar.className}</li>`;
    }

    const toggler = document.querySelector('.navbar-toggler');
    if (toggler) {
        const isVisible = window.getComputedStyle(toggler).display !== 'none';
        results += `<li><strong>Bouton hamburger visible :</strong> ${isVisible ? 'Oui' : 'Non'}</li>`;
    }

    results += '</ul>';
    showResults(results);
}

function toggleNavbar() {
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        navbar.style.display = navbar.style.display === 'none' ? '' : 'none';
        setTimeout(() => {
            navbar.style.display = '';
        }, 2000);
    }
}

function showResults(content) {
    const resultsDiv = document.getElementById('test-results');
    const contentDiv = document.getElementById('test-content');

    contentDiv.innerHTML = content;
    resultsDiv.classList.remove('d-none');

    // Scroll vers les résultats
    resultsDiv.scrollIntoView({ behavior: 'smooth' });
}

// Mise à jour de la largeur d'écran
function updateScreenWidth() {
    const screenWidthElement = document.getElementById('screen-width');
    if (screenWidthElement) {
        const width = window.innerWidth;
        let type = '';
        if (width >= 1200) type = 'XL';
        else if (width >= 992) type = 'LG';
        else if (width >= 768) type = 'MD';
        else if (width >= 576) type = 'SM';
        else type = 'XS';

        screenWidthElement.textContent = `${width}px (${type})`;
    }
}

// Test automatique au chargement
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page de test navbar chargée');
    console.log('Largeur écran:', window.innerWidth);
    console.log('Bootstrap JS chargé:', typeof bootstrap !== 'undefined');

    // Mettre à jour la largeur d'écran
    updateScreenWidth();

    // Écouter les redimensionnements
    window.addEventListener('resize', updateScreenWidth);
});
</script>
@endpush
@endsection
