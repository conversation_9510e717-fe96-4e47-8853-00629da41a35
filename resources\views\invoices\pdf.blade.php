<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture #{{ $invoice->id }} - LocaSpace</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            float: left;
            width: 50%;
        }
        
        .invoice-info {
            float: right;
            width: 45%;
            text-align: right;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .invoice-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
        
        .client-info {
            margin: 30px 0;
        }
        
        .client-box {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        
        .reservation-box {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #28a745;
        }
        
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
        }
        
        .invoice-table th,
        .invoice-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        
        .invoice-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .totals-table {
            width: 300px;
            float: right;
            margin-top: 20px;
        }
        
        .totals-table td {
            padding: 8px;
            border: none;
        }
        
        .total-row {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        
        .payment-status {
            margin: 30px 0;
            padding: 15px;
            border-radius: 5px;
        }
        
        .payment-paid {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .payment-unpaid {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .terms {
            margin-top: 40px;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header clearfix">
        <div class="company-info">
            <div class="company-name">LocaSpace</div>
            <div>Plateforme de réservation de locaux</div>
            <div>Casablanca, Maroc</div>
            <div>Email: <EMAIL></div>
            <div>Tél: +212 5 22 XX XX XX</div>
        </div>
        <div class="invoice-info">
            <div class="invoice-title">FACTURE</div>
            <div><strong>Numéro:</strong> #{{ $invoice->id }}</div>
            <div><strong>Date:</strong> {{ $invoice->created_at->format('d/m/Y') }}</div>
            <div><strong>Échéance:</strong> {{ $invoice->created_at->addDays(30)->format('d/m/Y') }}</div>
        </div>
    </div>

    <!-- Client and Reservation Info -->
    <div class="client-info clearfix">
        <div style="float: left; width: 48%;">
            <h3>Facturé à:</h3>
            <div class="client-box">
                <strong>{{ $invoice->reservation->user->name }}</strong><br>
                {{ $invoice->reservation->user->email }}<br>
                Client depuis: {{ $invoice->reservation->user->created_at->format('d/m/Y') }}
            </div>
        </div>
        <div style="float: right; width: 48%;">
            <h3>Réservation:</h3>
            <div class="reservation-box">
                <strong>{{ $invoice->reservation->local->name }}</strong><br>
                {{ $invoice->reservation->local->location }}<br>
                Type: {{ ucfirst($invoice->reservation->local->type) }}<br>
                <small>Réservation #{{ $invoice->reservation->id }}</small>
            </div>
        </div>
    </div>

    <!-- Invoice Items -->
    <table class="invoice-table">
        <thead>
            <tr>
                <th>Description</th>
                <th class="text-center">Date</th>
                <th class="text-center">Durée</th>
                <th class="text-right">Prix unitaire</th>
                <th class="text-right">Total HT</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <strong>{{ $invoice->reservation->local->name }}</strong><br>
                    {{ ucfirst($invoice->reservation->local->type) }} - {{ $invoice->reservation->local->location }}<br>
                    <small>{{ $invoice->reservation->start_time }} - {{ $invoice->reservation->end_time }}</small>
                </td>
                <td class="text-center">{{ $invoice->reservation->date->format('d/m/Y') }}</td>
                <td class="text-center">{{ $invoice->reservation->getDurationInHours() }}h</td>
                <td class="text-right">{{ $invoice->reservation->local->price }}€</td>
                <td class="text-right">{{ $invoice->reservation->calculateAmount() }}€</td>
            </tr>
        </tbody>
    </table>

    <!-- Totals -->
    <div class="clearfix">
        <table class="totals-table">
            <tr>
                <td>Sous-total HT:</td>
                <td class="text-right">{{ $invoice->reservation->calculateAmount() }}€</td>
            </tr>
            <tr>
                <td>TVA (20%):</td>
                <td class="text-right">{{ number_format($invoice->reservation->calculateAmount() * 0.2, 2) }}€</td>
            </tr>
            <tr class="total-row">
                <td><strong>Total TTC:</strong></td>
                <td class="text-right"><strong>{{ $invoice->amount }}€</strong></td>
            </tr>
        </table>
    </div>

    <!-- Payment Status -->
    <div class="clearfix">
        <div class="payment-status {{ $invoice->isPaid() ? 'payment-paid' : 'payment-unpaid' }}">
            @if($invoice->isPaid())
                <strong>✓ FACTURE PAYÉE</strong><br>
                Paiement reçu le {{ $invoice->updated_at->format('d/m/Y à H:i') }}
            @else
                <strong>⚠ FACTURE NON PAYÉE</strong><br>
                En attente de paiement
            @endif
        </div>
    </div>

    <!-- Terms and Conditions -->
    <div class="terms">
        <h4>Conditions générales:</h4>
        <ul>
            <li>Paiement exigible à réception de facture</li>
            <li>Annulation possible jusqu'à 24h avant la réservation</li>
            <li>En cas de retard de paiement, des pénalités de 3% par mois peuvent s'appliquer</li>
            <li>Tout litige relève de la compétence des tribunaux de Casablanca</li>
            <li>Pour toute question, contactez-nous à <EMAIL></li>
        </ul>
        
        <p><strong>Informations légales:</strong></p>
        <p>
            LocaSpace SARL - RC: 123456 - IF: 7891011 - ICE: 001234567890123<br>
            Capital social: 100.000 DH - Siège social: Casablanca, Maroc
        </p>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>
            Cette facture a été générée automatiquement par LocaSpace le {{ now()->format('d/m/Y à H:i') }}<br>
            Pour toute question, contactez-nous à <EMAIL> ou visitez www.locaspace.com
        </p>
    </div>
</body>
</html>
