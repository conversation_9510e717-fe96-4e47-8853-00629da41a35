<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Mes réservations
                    </h1>
                    <p class="text-muted">G<PERSON>rez toutes vos réservations</p>
                </div>
                <div>
                    <a href="<?php echo e(route('locals.index')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Nouvelle réservation
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('reservations.index')); ?>">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="status" class="form-label">Statut</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="">Tous les statuts</option>
                                    <option value="confirmée" <?php echo e(request('status') === 'confirmée' ? 'selected' : ''); ?>>Confirmées</option>
                                    <option value="en attente" <?php echo e(request('status') === 'en attente' ? 'selected' : ''); ?>>En attente</option>
                                    <option value="annulée" <?php echo e(request('status') === 'annulée' ? 'selected' : ''); ?>>Annulées</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">Date de début</label>
                                <input type="date" name="date_from" id="date_from" class="form-control" 
                                       value="<?php echo e(request('date_from')); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">Date de fin</label>
                                <input type="date" name="date_to" id="date_to" class="form-control" 
                                       value="<?php echo e(request('date_to')); ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-filter me-1"></i>Filtrer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservations List -->
    <div class="row">
        <div class="col-12">
            <?php if($reservations->count() > 0): ?>
                <div class="card">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Local</th>
                                        <th>Date</th>
                                        <th>Heure</th>
                                        <th>Durée</th>
                                        <th>Statut</th>
                                        <th>Montant</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $reservations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reservation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <?php if($reservation->local->type === 'sport'): ?>
                                                        <i class="fas fa-futbol text-success fa-lg"></i>
                                                    <?php elseif($reservation->local->type === 'conference'): ?>
                                                        <i class="fas fa-presentation-screen text-primary fa-lg"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-glass-cheers text-warning fa-lg"></i>
                                                    <?php endif; ?>
                                                </div>
                                                <div>
                                                    <strong><?php echo e($reservation->local->name); ?></strong><br>
                                                    <small class="text-muted"><?php echo e($reservation->local->location); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong><?php echo e($reservation->date->format('d/m/Y')); ?></strong><br>
                                            <small class="text-muted"><?php echo e($reservation->date->format('l')); ?></small>
                                        </td>
                                        <td>
                                            <?php echo e($reservation->start_time); ?> - <?php echo e($reservation->end_time); ?>

                                        </td>
                                        <td>
                                            <?php echo e($reservation->getDurationInHours()); ?>h
                                        </td>
                                        <td>
                                            <?php if($reservation->status === 'confirmée'): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Confirmée
                                                </span>
                                            <?php elseif($reservation->status === 'en attente'): ?>
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>En attente
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>Annulée
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($reservation->invoice): ?>
                                                <strong><?php echo e($reservation->invoice->amount); ?>€</strong><br>
                                                <?php if($reservation->invoice->isPaid()): ?>
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle me-1"></i>Payé
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-danger">
                                                        <i class="fas fa-exclamation-circle me-1"></i>Non payé
                                                    </small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('reservations.show', $reservation)); ?>" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="Voir les détails">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                
                                                <?php if($reservation->invoice && $reservation->invoice->isPaid()): ?>
                                                    <a href="<?php echo e(route('invoices.download', $reservation->invoice)); ?>" 
                                                       class="btn btn-sm btn-outline-success" 
                                                       title="Télécharger la facture">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if(in_array($reservation->status, ['en attente', 'confirmée'])): ?>
                                                    <form method="POST" action="<?php echo e(route('reservations.cancel', $reservation)); ?>" 
                                                          style="display: inline;">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('PATCH'); ?>
                                                        <button type="submit" 
                                                                class="btn btn-sm btn-outline-danger" 
                                                                title="Annuler la réservation"
                                                                onclick="return confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if($reservations->hasPages()): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($reservations->appends(request()->query())->links()); ?>

                </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h4>Aucune réservation trouvée</h4>
                    <p class="text-muted">
                        <?php if(request()->hasAny(['status', 'date_from', 'date_to'])): ?>
                            Aucune réservation ne correspond à vos critères de recherche.
                        <?php else: ?>
                            Vous n'avez pas encore effectué de réservation.
                        <?php endif; ?>
                    </p>
                    <div class="mt-4">
                        <?php if(request()->hasAny(['status', 'date_from', 'date_to'])): ?>
                            <a href="<?php echo e(route('reservations.index')); ?>" class="btn btn-outline-primary me-2">
                                <i class="fas fa-refresh me-2"></i>Réinitialiser les filtres
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo e(route('locals.index')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Faire une réservation
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\hackathon\locaspace\resources\views/reservations/index.blade.php ENDPATH**/ ?>