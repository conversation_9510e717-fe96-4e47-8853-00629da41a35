@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ route('reservations.index') }}">Mes réservations</a></li>
            <li class="breadcrumb-item active">Réservation #{{ $reservation->id }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-calendar-check me-2"></i>Détails de la réservation
                        </h4>
                        <div>
                            @if($reservation->status === 'confirmée')
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-check me-1"></i>Confirmée
                                </span>
                            @elseif($reservation->status === 'en attente')
                                <span class="badge bg-warning fs-6">
                                    <i class="fas fa-clock me-1"></i>En attente
                                </span>
                            @else
                                <span class="badge bg-danger fs-6">
                                    <i class="fas fa-times me-1"></i>Annulée
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Reservation Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5><i class="fas fa-info-circle text-primary me-2"></i>Informations de réservation</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Numéro :</strong></td>
                                    <td>#{{ $reservation->id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Date :</strong></td>
                                    <td>{{ $reservation->date->format('d/m/Y') }} ({{ $reservation->date->format('l') }})</td>
                                </tr>
                                <tr>
                                    <td><strong>Heure :</strong></td>
                                    <td>{{ $reservation->start_time }} - {{ $reservation->end_time }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Durée :</strong></td>
                                    <td>{{ $reservation->getDurationInHours() }} heure(s)</td>
                                </tr>
                                <tr>
                                    <td><strong>Créée le :</strong></td>
                                    <td>{{ $reservation->created_at->format('d/m/Y à H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-building text-primary me-2"></i>Local réservé</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        @if($reservation->local->type === 'sport')
                                            <i class="fas fa-futbol text-success fa-2x me-3"></i>
                                        @elseif($reservation->local->type === 'conference')
                                            <i class="fas fa-presentation-screen text-primary fa-2x me-3"></i>
                                        @else
                                            <i class="fas fa-glass-cheers text-warning fa-2x me-3"></i>
                                        @endif
                                        <div>
                                            <h6 class="mb-0">{{ $reservation->local->name }}</h6>
                                            <small class="text-muted">{{ ucfirst($reservation->local->type) }}</small>
                                        </div>
                                    </div>
                                    <p class="mb-2">
                                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                        {{ $reservation->local->location }}
                                    </p>
                                    <p class="mb-2">
                                        <i class="fas fa-users text-muted me-2"></i>
                                        Capacité : {{ $reservation->local->capacity }} personnes
                                    </p>
                                    <p class="mb-0">
                                        <i class="fas fa-euro-sign text-muted me-2"></i>
                                        Prix : {{ $reservation->local->price }}€/heure
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Equipment -->
                    @if($reservation->local->equipment && count($reservation->local->equipment) > 0)
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5><i class="fas fa-cogs text-primary me-2"></i>Équipements inclus</h5>
                            <div class="d-flex flex-wrap gap-2">
                                @foreach($reservation->local->equipment as $equipment)
                                    <span class="badge bg-light text-dark border">
                                        @if($equipment === 'wifi')
                                            <i class="fas fa-wifi me-1"></i>WiFi
                                        @elseif($equipment === 'projecteur')
                                            <i class="fas fa-video me-1"></i>Projecteur
                                        @elseif($equipment === 'climatisation')
                                            <i class="fas fa-snowflake me-1"></i>Climatisation
                                        @else
                                            <i class="fas fa-check me-1"></i>{{ $equipment }}
                                        @endif
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('reservations.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Retour à mes réservations
                                </a>

                                <div>
                                    @if(in_array($reservation->status, ['en attente', 'confirmée']))
                                        <form method="POST" action="{{ route('reservations.cancel', $reservation) }}"
                                              style="display: inline;">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit"
                                                    class="btn btn-outline-danger"
                                                    onclick="return confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')">
                                                <i class="fas fa-times me-2"></i>Annuler la réservation
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Invoice Card -->
            @if($reservation->invoice)
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-invoice me-2"></i>Facture
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <h4 class="text-success">{{ $reservation->invoice->amount }}€</h4>
                        <p class="text-muted mb-0">Montant total</p>
                    </div>

                    <div class="mb-3">
                        <strong>Statut du paiement :</strong><br>
                        @if($reservation->invoice->isPaid())
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Payé
                            </span>
                        @else
                            <span class="badge bg-danger">
                                <i class="fas fa-exclamation-circle me-1"></i>Non payé
                            </span>
                        @endif
                    </div>

                    <div class="d-grid gap-2">
                        @if($reservation->invoice->isPaid())
                            <a href="{{ route('invoices.show', $reservation->invoice) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>Voir la facture
                            </a>
                            <a href="{{ route('invoices.download', $reservation->invoice) }}" class="btn btn-success">
                                <i class="fas fa-download me-2"></i>Télécharger PDF
                            </a>
                        @else
                            <a href="{{ route('invoices.show', $reservation->invoice) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>Voir la facture
                            </a>
                            <button type="button" class="btn btn-warning w-100" id="payButton"
                                    data-invoice-id="{{ $reservation->invoice->id }}"
                                    data-amount="{{ $reservation->invoice->amount }}">
                                <i class="fas fa-credit-card me-2"></i>Payer maintenant
                            </button>
                        @endif
                    </div>
                </div>
            </div>
            @endif

            <!-- Contact Support -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-headset me-2"></i>Besoin d'aide ?
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        Une question concernant votre réservation ? Notre équipe est là pour vous aider.
                    </p>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>Contacter le support
                        </button>
                    </div>
                </div>
            </div>

            <!-- Reservation Timeline -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Historique
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Réservation créée</h6>
                                <small class="text-muted">{{ $reservation->created_at->format('d/m/Y à H:i') }}</small>
                            </div>
                        </div>

                        @if($reservation->status === 'confirmée')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Réservation confirmée</h6>
                                <small class="text-muted">{{ $reservation->updated_at->format('d/m/Y à H:i') }}</small>
                            </div>
                        </div>
                        @elseif($reservation->status === 'annulée')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Réservation annulée</h6>
                                <small class="text-muted">{{ $reservation->updated_at->format('d/m/Y à H:i') }}</small>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #dee2e6;
}

.payment-loading {
    display: none;
}

.payment-success {
    display: none;
}
</style>
@endpush

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const payButton = document.getElementById('payButton');

    if (payButton) {
        payButton.addEventListener('click', function() {
            const invoiceId = this.dataset.invoiceId;
            const amount = this.dataset.amount;

            // Afficher le loading
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Traitement...';
            this.disabled = true;

            // Créer le Payment Intent
            fetch('/api/stripe/create-payment-intent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    invoice_id: invoiceId,
                    amount: amount
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Réponse API:', data);

                if (data.success) {
                    // Si c'est une session de test
                    if (data.session_id.startsWith('test_session_')) {
                        alert('Test réussi ! Session ID: ' + data.session_id + '\nDébug: ' + JSON.stringify(data.debug, null, 2));

                        // Restaurer le bouton
                        payButton.innerHTML = '<i class="fas fa-credit-card me-2"></i>Payer maintenant';
                        payButton.disabled = false;
                        return;
                    }

                    // Initialiser Stripe pour une vraie session
                    const stripe = Stripe('{{ config("services.stripe.key") }}');

                    // Rediriger vers Stripe Checkout
                    return stripe.redirectToCheckout({
                        sessionId: data.session_id
                    });
                } else {
                    throw new Error(data.error || 'Erreur lors de la création du paiement');
                }
            })
            .then(result => {
                if (result.error) {
                    throw new Error(result.error.message);
                }
            })
            .catch(error => {
                console.error('Erreur de paiement:', error);

                // Restaurer le bouton
                payButton.innerHTML = '<i class="fas fa-credit-card me-2"></i>Payer maintenant';
                payButton.disabled = false;

                // Afficher l'erreur
                alert('Erreur lors du paiement: ' + error.message);
            });
        });
    }
});
</script>
@endpush
@endsection
