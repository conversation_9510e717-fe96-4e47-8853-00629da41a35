@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-book me-2"></i>Guide de test - Navbar LocaSpace
            </h1>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>Tests réussis !</h5>
                <p class="mb-0">Votre navbar fonctionne correctement. Voici un résumé des tests effectués :</p>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-clipboard-check me-2"></i>Résultats des tests</h5>
                        </div>
                        <div class="card-body">
                            <h6>✅ Tests réussis :</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-desktop me-2 text-success"></i>Affichage Desktop (≥992px)</span>
                                    <span class="badge bg-success">OK</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-mobile-alt me-2 text-success"></i>Affichage Mobile (&lt;992px)</span>
                                    <span class="badge bg-success">OK</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-bars me-2 text-success"></i>Bouton hamburger</span>
                                    <span class="badge bg-success">Fonctionnel</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-expand-arrows-alt me-2 text-success"></i>Menu collapsible</span>
                                    <span class="badge bg-success">Responsive</span>
                                </li>
                            </ul>

                            <h6 class="mt-4">📊 Statistiques détectées :</h6>
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h4 class="text-primary">1920px</h4>
                                            <small>Largeur écran</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h4 class="text-success">XL</h4>
                                            <small>Type d'écran</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h4 class="text-info">0</h4>
                                            <small>Dropdowns (invité)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h4 class="text-warning">✓</h4>
                                            <small>Bootstrap JS</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5><i class="fas fa-lightbulb me-2"></i>Explications</h5>
                        </div>
                        <div class="card-body">
                            <h6>🔍 Pourquoi "Nombre de dropdowns: 0" ?</h6>
                            <p>C'est normal ! Vous n'êtes pas connecté, donc les menus déroulants suivants ne sont pas visibles :</p>
                            <ul>
                                <li><strong>Menu Admin</strong> : Visible uniquement pour les administrateurs</li>
                                <li><strong>Menu Utilisateur</strong> : Visible uniquement pour les utilisateurs connectés</li>
                            </ul>

                            <h6 class="mt-3">🍔 Comportement du bouton hamburger :</h6>
                            <p>Le message "Menu fermé" après clic est normal car :</p>
                            <ol>
                                <li>Le menu était déjà fermé au départ</li>
                                <li>Le clic l'ouvre, puis Bootstrap le referme automatiquement</li>
                                <li>C'est le comportement attendu sur un écran large (XL)</li>
                            </ol>

                            <h6 class="mt-3">📱 Pour tester le mode mobile :</h6>
                            <ol>
                                <li>Ouvrez les outils développeur (F12)</li>
                                <li>Activez le mode responsive</li>
                                <li>Choisissez une taille mobile (ex: iPhone)</li>
                                <li>Le bouton hamburger devrait alors fonctionner normalement</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tools me-2"></i>Actions de test</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('test.navbar') }}" class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>Test mode invité
                                </a>
                                <a href="{{ route('test.navbar.auth') }}" class="btn btn-success">
                                    <i class="fas fa-user me-2"></i>Test mode connecté
                                </a>
                                <button class="btn btn-info" onclick="openDevTools()">
                                    <i class="fas fa-code me-2"></i>Ouvrir console
                                </button>
                                <button class="btn btn-warning" onclick="simulateResize()">
                                    <i class="fas fa-mobile-alt me-2"></i>Simuler mobile
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5><i class="fas fa-terminal me-2"></i>Commandes console</h5>
                        </div>
                        <div class="card-body">
                            <p class="small">Tapez ces commandes dans la console (F12) :</p>
                            <div class="bg-dark text-light p-2 rounded small">
                                <code>testNavbar()</code><br>
                                <code>testResponsive()</code><br>
                                <code>testDropdowns()</code><br>
                                <code>simulateMobile()</code><br>
                                <code>simulateDesktop()</code>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle me-2"></i>Informations</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><strong>Framework :</strong> Bootstrap 5.3</li>
                                <li><strong>Responsive :</strong> Mobile-first</li>
                                <li><strong>Breakpoint :</strong> 992px (LG)</li>
                                <li><strong>JavaScript :</strong> Vanilla + Bootstrap</li>
                                <li><strong>Icons :</strong> Font Awesome 6</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function openDevTools() {
    alert('Appuyez sur F12 pour ouvrir les outils développeur, puis tapez les commandes dans la console.');
}

function simulateResize() {
    // Simuler un redimensionnement
    const originalWidth = window.innerWidth;
    
    // Créer un overlay pour simuler mobile
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.8);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        text-align: center;
    `;
    overlay.innerHTML = `
        <div>
            <h3>Mode simulation mobile</h3>
            <p>Redimensionnez votre fenêtre ou utilisez les outils développeur</p>
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                Fermer
            </button>
        </div>
    `;
    
    document.body.appendChild(overlay);
    
    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (overlay.parentElement) {
            overlay.remove();
        }
    }, 5000);
}
</script>
@endpush
@endsection
