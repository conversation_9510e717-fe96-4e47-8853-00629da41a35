<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord administrateur
            </h1>
            <p class="text-muted">Vue d'ensemble de la plateforme LocaSpace</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($stats['total_users']); ?></h4>
                            <p class="mb-0">Utilisateurs</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-primary bg-opacity-75">
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="text-white text-decoration-none">
                        <small>Voir tous <i class="fas fa-arrow-right ms-1"></i></small>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($stats['total_locals']); ?></h4>
                            <p class="mb-0">Locaux</p>
                            <small>(<?php echo e($stats['active_locals']); ?> actifs)</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-success bg-opacity-75">
                    <a href="<?php echo e(route('admin.locals.index')); ?>" class="text-white text-decoration-none">
                        <small>Gérer <i class="fas fa-arrow-right ms-1"></i></small>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($stats['total_reservations']); ?></h4>
                            <p class="mb-0">Réservations</p>
                            <small>(<?php echo e($stats['pending_reservations']); ?> en attente)</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-info bg-opacity-75">
                    <a href="<?php echo e(route('admin.reservations.index')); ?>" class="text-white text-decoration-none">
                        <small>Voir toutes <i class="fas fa-arrow-right ms-1"></i></small>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e(number_format($stats['total_revenue'], 0)); ?>€</h4>
                            <p class="mb-0">Revenus</p>
                            <small>(<?php echo e(number_format($stats['pending_payments'], 0)); ?>€ en attente)</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-euro-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-warning bg-opacity-75">
                    <a href="<?php echo e(route('admin.invoices.index')); ?>" class="text-white text-decoration-none">
                        <small>Voir factures <i class="fas fa-arrow-right ms-1"></i></small>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Reservations -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>Réservations récentes
                        </h5>
                        <a href="<?php echo e(route('admin.reservations.index')); ?>" class="btn btn-sm btn-outline-primary">
                            Voir toutes
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if($recentReservations->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Client</th>
                                        <th>Local</th>
                                        <th>Date</th>
                                        <th>Statut</th>
                                        <th>Montant</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentReservations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reservation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo e($reservation->user->name); ?></strong><br>
                                            <small class="text-muted"><?php echo e($reservation->user->email); ?></small>
                                        </td>
                                        <td>
                                            <strong><?php echo e($reservation->local->name); ?></strong><br>
                                            <small class="text-muted"><?php echo e($reservation->local->location); ?></small>
                                        </td>
                                        <td>
                                            <?php echo e($reservation->date->format('d/m/Y')); ?><br>
                                            <small class="text-muted"><?php echo e($reservation->start_time); ?> - <?php echo e($reservation->end_time); ?></small>
                                        </td>
                                        <td>
                                            <?php if($reservation->status === 'confirmée'): ?>
                                                <span class="badge bg-success">Confirmée</span>
                                            <?php elseif($reservation->status === 'en attente'): ?>
                                                <span class="badge bg-warning">En attente</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Annulée</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($reservation->invoice): ?>
                                                <?php echo e($reservation->invoice->amount); ?>€
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('reservations.show', $reservation)); ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if($reservation->status === 'en attente'): ?>
                                                    <form method="POST" action="<?php echo e(route('admin.reservations.confirm', $reservation)); ?>" 
                                                          style="display: inline;">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('PATCH'); ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-success">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune réservation récente</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions & Charts -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Actions rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.locals.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Ajouter un local
                        </a>
                        <a href="<?php echo e(route('admin.reservations.index', ['status' => 'en attente'])); ?>" class="btn btn-warning">
                            <i class="fas fa-clock me-2"></i>Réservations en attente
                        </a>
                        <a href="<?php echo e(route('admin.reports')); ?>" class="btn btn-info">
                            <i class="fas fa-chart-bar me-2"></i>Voir les rapports
                        </a>
                        <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-users me-2"></i>Gérer les utilisateurs
                        </a>
                    </div>
                </div>
            </div>

            <!-- Monthly Revenue Chart -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Revenus mensuels
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($monthlyRevenue->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Mois</th>
                                        <th class="text-end">Revenus</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $monthlyRevenue->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $revenue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e(\Carbon\Carbon::parse($revenue->year . '-' . $revenue->month . '-01')->format('M Y')); ?></td>
                                        <td class="text-end"><?php echo e(number_format($revenue->total, 0)); ?>€</td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="<?php echo e(route('admin.reports')); ?>" class="btn btn-sm btn-outline-primary">
                                Voir le rapport complet
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Aucune donnée disponible</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-server me-2"></i>État du système
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                                <h6>Système</h6>
                                <small class="text-success">Opérationnel</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <i class="fas fa-database text-success fa-2x mb-2"></i>
                                <h6>Base de données</h6>
                                <small class="text-success">Connectée</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <i class="fas fa-envelope text-success fa-2x mb-2"></i>
                                <h6>Email</h6>
                                <small class="text-success">Fonctionnel</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-credit-card text-success fa-2x mb-2"></i>
                            <h6>Paiements</h6>
                            <small class="text-success">Actifs</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\hackathon\locaspace\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>