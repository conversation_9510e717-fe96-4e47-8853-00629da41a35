@props([
    'title',
    'value',
    'subtitle' => null,
    'icon',
    'color' => 'primary',
    'url' => null,
    'linkText' => 'Voir plus'
])

@php
    $colorClasses = [
        'primary' => 'bg-primary',
        'success' => 'bg-success',
        'info' => 'bg-info',
        'warning' => 'bg-warning',
        'danger' => 'bg-danger',
        'secondary' => 'bg-secondary',
        'dark' => 'bg-dark'
    ];
    
    $bgClass = $colorClasses[$color] ?? 'bg-primary';
@endphp

<div class="card {{ $bgClass }} text-white h-100">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0 fw-bold">{{ $value }}</h4>
                <p class="mb-0">{{ $title }}</p>
                @if($subtitle)
                    <small class="opacity-75">{{ $subtitle }}</small>
                @endif
            </div>
            <div class="align-self-center">
                <i class="{{ $icon }} fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    @if($url)
        <div class="card-footer {{ $bgClass }} bg-opacity-75 border-0">
            <a href="{{ $url }}" class="text-white text-decoration-none">
                <small>{{ $linkText }} <i class="fas fa-arrow-right ms-1"></i></small>
            </a>
        </div>
    @endif
</div>
