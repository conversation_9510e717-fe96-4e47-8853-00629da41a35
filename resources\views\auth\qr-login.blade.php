@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning text-dark text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-qrcode me-2"></i>Connexion QR Code
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Instructions -->
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Comment ça marche :</strong>
                        <ol class="mb-0 mt-2">
                            <li>Saisissez votre code QR personnel</li>
                            <li>Ou scannez-le avec votre appareil photo</li>
                            <li>Connectez-vous instantanément !</li>
                        </ol>
                    </div>

                    <form method="POST" action="{{ route('qr.login') }}">
                        @csrf

                        <!-- QR Code Input -->
                        <div class="mb-4">
                            <label for="qr_code" class="form-label">
                                <i class="fas fa-qrcode me-1"></i>Code QR
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg @error('qr_code') is-invalid @enderror" 
                                   id="qr_code" 
                                   name="qr_code" 
                                   placeholder="Saisissez ou scannez votre QR code" 
                                   required 
                                   autofocus>
                            @error('qr_code')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- QR Scanner Button -->
                        <div class="mb-4 text-center">
                            <button type="button" class="btn btn-outline-primary" id="scanQR">
                                <i class="fas fa-camera me-2"></i>Scanner avec la caméra
                            </button>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Se connecter avec QR
                            </button>
                        </div>
                    </form>

                    <!-- Divider -->
                    <hr class="my-4">

                    <!-- Alternative Login Methods -->
                    <div class="text-center">
                        <p class="text-muted mb-3">Ou utilisez la méthode classique :</p>
                        <a href="{{ route('login') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-envelope me-2"></i>Email / Mot de passe
                        </a>
                    </div>

                    <!-- Help Section -->
                    <div class="mt-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-question-circle me-2"></i>Où trouver mon QR code ?
                                </h6>
                                <p class="card-text small mb-0">
                                    Votre QR code personnel est disponible dans votre profil après inscription. 
                                    Vous pouvez l'imprimer ou le sauvegarder sur votre téléphone pour un accès rapide.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Links -->
                    <div class="text-center mt-4">
                        <p class="mb-0">
                            Pas encore de compte ? 
                            <a href="{{ route('register') }}" class="text-decoration-none">
                                S'inscrire gratuitement
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.getElementById('scanQR').addEventListener('click', function() {
    // Placeholder for QR scanner functionality
    // In a real implementation, you would integrate with a QR scanner library
    alert('Fonctionnalité de scan QR à implémenter avec une bibliothèque comme QuaggaJS ou ZXing');
});
</script>
@endpush
@endsection
