<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'type' => 'info',
    'dismissible' => true,
    'icon' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'type' => 'info',
    'dismissible' => true,
    'icon' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $alertClasses = [
        'success' => 'alert-success',
        'error' => 'alert-danger',
        'warning' => 'alert-warning',
        'info' => 'alert-info',
        'primary' => 'alert-primary',
        'secondary' => 'alert-secondary',
        'light' => 'alert-light',
        'dark' => 'alert-dark'
    ];
    
    $alertIcons = [
        'success' => 'fas fa-check-circle',
        'error' => 'fas fa-exclamation-triangle',
        'warning' => 'fas fa-exclamation-circle',
        'info' => 'fas fa-info-circle',
        'primary' => 'fas fa-info-circle',
        'secondary' => 'fas fa-info-circle',
        'light' => 'fas fa-info-circle',
        'dark' => 'fas fa-info-circle'
    ];
    
    $alertClass = $alertClasses[$type] ?? 'alert-info';
    $defaultIcon = $alertIcons[$type] ?? 'fas fa-info-circle';
    $iconClass = $icon ?? $defaultIcon;
?>

<div class="alert <?php echo e($alertClass); ?> <?php echo e($dismissible ? 'alert-dismissible fade show' : ''); ?>" role="alert">
    <?php if($iconClass): ?>
        <i class="<?php echo e($iconClass); ?> me-2"></i>
    <?php endif; ?>
    
    <?php echo e($slot); ?>

    
    <?php if($dismissible): ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    <?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\hackathon\locaspace\resources\views/components/alert.blade.php ENDPATH**/ ?>